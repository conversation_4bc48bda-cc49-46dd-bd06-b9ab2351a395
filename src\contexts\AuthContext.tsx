import React, { createContext, useContext, useEffect, useState } from "react";
import {
  User,
  signOut,
  onAuthStateChanged,
  signInWithPopup,
  OAuthProvider,
  setPersistence,
  browserSessionPersistence,
  getRedirectResult
} from "firebase/auth";
import { auth } from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { clearAuthStorage, clearReactQueryCache, secureLog } from "@/lib/securityUtils";

// Extend Window interface for additional properties
declare global {
  interface Window {
    __REACT_QUERY_CACHE__?: any;
    gc?: () => void;
  }
}

type AuthContextType = {
  currentUser: User | null;
  loading: boolean;
  loginWithMicrosoft: () => Promise<void>;
  logout: () => Promise<void>;
  clearAllStorage: () => void;
};

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const { toast } = useToast();

  // Create Microsoft OAuth provider
  const createMicrosoftProvider = () => {
    const provider = new OAuthProvider('microsoft.com');
    
    // Add required scopes
    provider.addScope('email');
    provider.addScope('profile');
    provider.addScope('openid');

    // Configure for single-tenant application
    const tenantId = import.meta.env.VITE_AZURE_TENANT_ID;
    
    if (tenantId) {
      provider.setCustomParameters({
        tenant: tenantId,
        prompt: 'select_account',
        domain_hint: 'organizations'
      });
    }

    return provider;
  };

  const loginWithMicrosoft = async () => {
    if (isAuthenticating) {
      secureLog('Authentication already in progress, ignoring request');
      return;
    }

    try {
      setIsAuthenticating(true);
      
      // Set session persistence
      await setPersistence(auth, browserSessionPersistence);

      const provider = createMicrosoftProvider();
      
      // Check if we have a tenant ID
      const tenantId = import.meta.env.VITE_AZURE_TENANT_ID;
      if (!tenantId) {
        throw new Error('Microsoft authentication is not properly configured. Missing tenant ID.');
      }

      secureLog('Starting Microsoft authentication with popup...');

      // Use popup method for better user experience
      const result = await signInWithPopup(auth, provider);
      
      secureLog('Authentication successful:', result.user.email);
      
      toast({
        title: "Success",
        description: "Successfully signed in with Microsoft"
      });

    } catch (error: any) {
      secureLog('Microsoft login error:', error);

      let errorMessage = "Failed to authenticate with Microsoft";
      let shouldShowError = true;

      // Handle specific error cases
      if (error.code === 'auth/popup-closed-by-user') {
        errorMessage = "Login was cancelled. Please try again and complete the Microsoft login process.";
      } else if (error.code === 'auth/popup-blocked') {
        errorMessage = "Popup was blocked by your browser. Please allow popups for this site and try again.";
      } else if (error.code === 'auth/cancelled-popup-request') {
        errorMessage = "Login request was cancelled. Please try again.";
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = "Network error. Please check your internet connection and try again.";
      } else if (error.code === 'auth/invalid-tenant-id') {
        errorMessage = "Invalid tenant configuration. Please contact your administrator.";
      } else if (error.code === 'auth/unauthorized-domain') {
        errorMessage = "This domain is not authorized for authentication. Please contact your administrator.";
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = "Microsoft sign-in is not enabled. Please contact your administrator.";
      } else if (error.code === 'auth/internal-error') {
        errorMessage = "Authentication service temporarily unavailable. Please refresh the page and try again.";
        // Try to clear any corrupted auth state
        try {
          await signOut(auth);
        } catch (signOutError) {
          console.warn('Failed to clear auth state:', signOutError);
        }
      } else if (error.code === 'auth/web-storage-unsupported') {
        errorMessage = "Your browser doesn't support the required storage. Please try a different browser.";
      } else if (error.code === 'auth/configuration-not-found') {
        errorMessage = "Authentication configuration error. Please contact support.";
      } else if (error.code === 'auth/app-deleted') {
        errorMessage = "Application configuration error. Please refresh the page.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Don't show error for user cancellation in some cases
      if (error.code === 'auth/popup-closed-by-user' && error.message.includes('closed')) {
        shouldShowError = false;
        secureLog('User likely closed popup intentionally');
      }

      if (shouldShowError) {
        toast({
          title: "Microsoft login failed",
          description: errorMessage,
          variant: "destructive"
        });
      }
      
      throw error;
    } finally {
      setIsAuthenticating(false);
    }
  };

  const logout = async () => {
    try {
      // Sign out from Firebase
      await signOut(auth);
      
      // Clear all storage and cache
      clearAuthStorage();
      clearReactQueryCache();
      
      // Reset user state
      setCurrentUser(null);

      toast({
        title: "Logged out",
        description: "Successfully logged out"
      });
    } catch (error: any) {
      secureLog('Logout error:', error);
      
      // Even if Firebase logout fails, clear local storage
      clearAuthStorage();
      clearReactQueryCache();
      setCurrentUser(null);
      
      toast({
        title: "Logout completed",
        description: "You have been logged out"
      });
    }
  };

  const clearAllStorage = () => {
    clearAuthStorage();
    clearReactQueryCache();
  };

  useEffect(() => {
    // Handle any redirect result (in case popup fails and redirect is used)
    const handleRedirectResult = async () => {
      try {
        const result = await getRedirectResult(auth);
        if (result) {
          secureLog('Redirect authentication successful:', result.user.email);
          toast({
            title: "Success",
            description: "Successfully signed in with Microsoft"
          });
        }
      } catch (error: any) {
        secureLog('Redirect result error:', error);
        // Handle redirect errors silently unless they're critical
        if (error.code !== 'auth/popup-closed-by-user') {
          toast({
            title: "Authentication failed",
            description: "Please try logging in again.",
            variant: "destructive"
          });
        }
      }
    };

    // Handle redirect result on app load
    handleRedirectResult();

    // Set up auth state listener
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      secureLog('Auth state changed:', user ? `User logged in: ${user.email}` : 'User logged out');
      setCurrentUser(user);
      setLoading(false);
      
      // Reset authenticating state when user successfully logs in
      if (user) {
        setIsAuthenticating(false);
      }
    });

    return unsubscribe;
  }, [toast]);

  const value = {
    currentUser,
    loading,
    loginWithMicrosoft,
    logout,
    clearAllStorage
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};