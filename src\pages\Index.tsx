import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

import {
  CheckCircle,
  Shield,
  FileText,
  BarChart3,
  ChevronRight,
  Sparkles,
  Check,
  ArrowRight,
  Zap,
  LineChart,
  Clock,
  Award,
  Lightbulb,
  Play,
  Star,
  Users,
  TrendingUp,
  Eye,
  Brain,
  Target,
  Database,
  Gauge,
  Upload,
  Download,
  Search,
  AlertTriangle,
  Lock,
  DollarSign,
  PieChart,
  Calculator,
  TrendingDown,
  Briefcase,
  Building,
  CreditCard,
  Banknote,
  Wallet,
  Receipt,
  ChevronDown,
  Phone,
  Mail,
  MapPin,
  Send,
  Globe,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  HelpCircle,
  Plus,
  Minus,
  CheckSquare,
  FileCheck,
  UserCheck,
  PhoneCall,
  MessageSquare,
  Calendar,
  Bookmark,
  Menu,
  X
} from "lucide-react";

const Index = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openFaq, setOpenFaq] = useState(null);

  // Navigation scroll effect
  const [isScrolled, setIsScrolled] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white overflow-x-hidden">
      {/* Navigation */}
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-slate-900/95 backdrop-blur-lg border-b border-white/10' : 'bg-transparent'
      }`}>
        <div className="w-full px-6 md:px-12 lg:px-20">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <div className="flex items-center gap-3">              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
                P
              </div>
              <span className="text-xl font-bold text-white">PKF Audit AI</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              <a href="#home" className="text-white/80 hover:text-white transition-colors">Home</a>
              <a href="#features" className="text-white/80 hover:text-white transition-colors">Features</a>
              <a href="#about" className="text-white/80 hover:text-white transition-colors">About</a>
              <a href="#why-choose" className="text-white/80 hover:text-white transition-colors">Why Choose Us</a>
              <a href="#contact" className="text-white/80 hover:text-white transition-colors">Contact</a>
              <a href="#faq" className="text-white/80 hover:text-white transition-colors">FAQ</a>
            </div>            {/* CTA Buttons */}
            <div className="hidden lg:flex items-center gap-4">
              <Link to="/login">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-8 py-3 text-lg font-semibold">
                  Get Started
                </Button>
              </Link>
            </div>

            {/* Mobile menu button */}
            <button 
              className="lg:hidden text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden bg-slate-900/95 backdrop-blur-lg border-t border-white/10 py-6">
              <div className="flex flex-col space-y-4">
                <a href="#home" className="text-white/80 hover:text-white transition-colors">Home</a>
                <a href="#features" className="text-white/80 hover:text-white transition-colors">Features</a>
                <a href="#about" className="text-white/80 hover:text-white transition-colors">About</a>
                <a href="#why-choose" className="text-white/80 hover:text-white transition-colors">Why Choose Us</a>
                <a href="#contact" className="text-white/80 hover:text-white transition-colors">Contact</a>
                <a href="#faq" className="text-white/80 hover:text-white transition-colors">FAQ</a>                <div className="flex flex-col gap-3 pt-4">
                  <Link to="/login">
                    <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-lg py-3">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="min-h-screen relative overflow-hidden flex items-center pt-20">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900"></div>
        
        {/* Financial background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2359B6F4' fill-opacity='0.4'%3E%3Cpath d='M30 30c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20zm0 0c0 11.046-8.954 20-20 20S-10 41.046-10 30s8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '120px 120px'
          }}></div>
        </div>

        {/* Floating financial icons */}
        <div className="absolute inset-0 pointer-events-none">
          <DollarSign className="absolute top-20 right-20 h-16 w-16 text-green-400/20 animate-pulse" style={{animationDelay: '0s'}} />
          <PieChart className="absolute top-40 left-20 h-12 w-12 text-blue-400/20 animate-pulse" style={{animationDelay: '1s'}} />
          <Calculator className="absolute bottom-40 right-40 h-14 w-14 text-purple-400/20 animate-pulse" style={{animationDelay: '2s'}} />
          <TrendingUp className="absolute bottom-60 left-40 h-10 w-10 text-emerald-400/20 animate-pulse" style={{animationDelay: '3s'}} />
          <Briefcase className="absolute top-1/3 left-1/3 h-8 w-8 text-indigo-400/20 animate-pulse" style={{animationDelay: '4s'}} />
          <Building className="absolute top-2/3 right-1/3 h-12 w-12 text-cyan-400/20 animate-pulse" style={{animationDelay: '5s'}} />
        </div>

        {/* Floating orbs */}
        <div className="absolute top-20 right-20 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>

        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left content */}
            <div className="space-y-8">
              <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white">
                <Sparkles className="h-5 w-5 mr-2 text-blue-300" />                <span className="font-medium">PKF Revolutionary Financial AI Technology</span>
                <div className="ml-3 px-2 py-1 bg-green-500/20 rounded-full text-xs text-green-300">NEW</div>
              </div>

              <h1 className="text-6xl md:text-7xl lg:text-8xl font-black leading-none">
                <span className="block bg-gradient-to-r from-white via-blue-200 to-white bg-clip-text text-transparent">
                  Future of
                </span>
                <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Financial Audit
                </span>
              </h1>

              <p className="text-2xl text-white/90 leading-relaxed">
                Transform your financial audit workflow with cutting-edge AI that analyzes reports, 
                ensures compliance, and identifies risks with <span className="text-green-400 font-semibold">99.9% accuracy</span>.
              </p>

              <div className="flex flex-col sm:flex-row gap-6">                <Link to="/login">
                  <Button 
                    size="lg" 
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-12 py-6 text-xl font-semibold shadow-2xl transform hover:scale-105 transition-all duration-300"
                  >
                    <Play className="h-6 w-6 mr-3" />
                    Start Free Trial
                  </Button>
                </Link>
                {/* <Button 
                  size="lg" 
                  variant="outline" 
                  className="bg-white/15 border-white/70 text-white hover:bg-white/30 hover:border-white font-semibold shadow-lg rounded-full px-12 py-6 text-xl backdrop-blur-sm"
                >
                  Watch Demo
                  <ArrowRight className="h-6 w-6 ml-3" />
                </Button> */}
              </div>

              {/* Trust indicators */}
              <div className="flex items-center gap-8 pt-8">
                <div className="flex items-center gap-2">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="text-white/80 ml-2">4.9/5 (2,000+ reviews)</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-300" />
                  <span className="text-white/80">10,000+ auditors</span>
                </div>
              </div>
            </div>

            {/* Right content - Financial Dashboard Preview */}
            <div className="relative">
              <div className="relative bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-3xl"></div>
                
                <div className="relative space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-bold text-white">Financial Dashboard</h3>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400 text-sm">Live Analysis</span>
                    </div>
                  </div>

                  {/* Financial metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <DollarSign className="h-8 w-8 text-green-400" />
                        <span className="text-white font-medium">Revenue</span>
                      </div>
                      <div className="text-2xl font-bold text-white">$2.4M</div>
                      <div className="text-green-400 text-sm">+12.5% ↗</div>
                    </div>
                    
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <TrendingUp className="h-8 w-8 text-blue-400" />
                        <span className="text-white font-medium">Growth</span>
                      </div>
                      <div className="text-2xl font-bold text-white">24.8%</div>
                      <div className="text-blue-400 text-sm">+5.2% ↗</div>
                    </div>
                  </div>

                  {/* Compliance status */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-white font-medium">Compliance Status</span>
                      <span className="text-green-400 text-sm">98.5%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-3">
                      <div className="bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full w-[98.5%]"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating financial metrics */}
              <div className="absolute -top-8 -right-8 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4 animate-pulse">
                <div className="flex items-center gap-3">
                  <PieChart className="h-8 w-8 text-purple-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">85%</div>
                    <div className="text-white/60 text-sm">Efficiency</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-8 -left-8 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4 animate-pulse" style={{animationDelay: '1s'}}>
                <div className="flex items-center gap-3">
                  <Shield className="h-8 w-8 text-emerald-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">100%</div>
                    <div className="text-white/60 text-sm">Secure</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/50 to-purple-900/50"></div>
        
        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="transform hover:scale-110 transition-all duration-500">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">
                10<span className="text-blue-300">K+</span>
              </div>
              <div className="text-white/80 font-medium">Active Users</div>
            </div>
            
            <div className="transform hover:scale-110 transition-all duration-500">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">
                99.9<span className="text-green-300">%</span>
              </div>
              <div className="text-white/80 font-medium">Accuracy Rate</div>
            </div>
            
            <div className="transform hover:scale-110 transition-all duration-500">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">
                $2.4<span className="text-yellow-300">B</span>
              </div>
              <div className="text-white/80 font-medium">Assets Analyzed</div>
            </div>
            
            <div className="transform hover:scale-110 transition-all duration-500">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">
                24<span className="text-purple-300">/7</span>
              </div>
              <div className="text-white/80 font-medium">AI Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900 to-indigo-900"></div>
        
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/30 to-transparent"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-gradient-to-tl from-purple-500/30 to-transparent"></div>
        </div>

        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white mb-8">
              <Calculator className="h-5 w-5 mr-2 text-blue-400" />
              <span className="font-medium">Financial AI Features</span>
            </div>
            
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              <span className="block">Advanced Financial</span>
              <span className="block bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Analysis Tools
              </span>
            </h2>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Our AI-powered platform combines cutting-edge technology with financial expertise 
              to revolutionize how you handle audit compliance and financial analysis.
            </p>
          </div>

          {/* Feature cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: "AI-Powered Analysis",
                description: "Advanced machine learning algorithms analyze financial documents with superhuman accuracy and speed.",
                color: "from-blue-500 to-blue-600"
              },
              {
                icon: PieChart,
                title: "Financial Insights",
                description: "Get deep insights into financial patterns, trends, and anomalies across all your audit documents.",
                color: "from-purple-500 to-purple-600"
              },
              {
                icon: Shield,
                title: "Compliance Monitoring",
                description: "Real-time compliance checks ensure adherence to financial regulations and audit standards.",
                color: "from-green-500 to-green-600"
              },
              {
                icon: Calculator,
                title: "Automated Calculations",
                description: "Automatically verify calculations and cross-reference figures across multiple documents.",
                color: "from-orange-500 to-red-500"
              },
              {
                icon: TrendingUp,
                title: "Risk Assessment",
                description: "Identify potential financial risks and irregularities before they become major issues.",
                color: "from-emerald-500 to-teal-500"
              },
              {
                icon: Database,
                title: "Data Integration",
                description: "Seamlessly integrate with existing financial systems and databases for comprehensive analysis.",
                color: "from-indigo-500 to-purple-500"
              }
            ].map((feature, index) => (
              <div key={index} className="group relative bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:bg-white/10 transition-all duration-700 transform hover:scale-105">
                <div className={`h-16 w-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-blue-200 transition-colors">
                  {feature.title}
                </h3>
                
                <p className="text-white/80 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-900 to-purple-900"></div>
        
        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white">
                <Building className="h-5 w-5 mr-2 text-indigo-400" />
                <span className="font-medium">About Our Company</span>
              </div>
              
              <h2 className="text-5xl md:text-6xl font-bold text-white">
                <span className="block">Leading the</span>
                <span className="block bg-gradient-to-r from-indigo-400 to-pink-400 bg-clip-text text-transparent">
                  Financial AI Revolution
                </span>
              </h2>
              
              <p className="text-xl text-white/80 leading-relaxed">
                Founded by financial experts and AI pioneers, we're transforming how organizations 
                approach audit compliance and financial analysis. Our mission is to make financial 
                auditing more accurate, efficient, and accessible to businesses of all sizes.
              </p>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-6 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-bold text-white mb-2">1978</div>
                  <div className="text-white/60">Founded</div>
                </div>
                <div className="text-center p-6 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-bold text-white mb-2">800+</div>
                  <div className="text-white/60">Team Members</div>
                </div>
                <div className="text-center p-6 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-bold text-white mb-2">30+</div>
                  <div className="text-white/60">Countries</div>
                </div>
                <div className="text-center p-6 bg-white/5 rounded-2xl border border-white/10">
                  <div className="text-3xl font-bold text-white mb-2">$103.9</div>
                  <div className="text-white/60">Assets Secured</div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=2126&q=80"
                  alt="Financial team"
                  className="rounded-2xl w-full"
                />
                <div className="absolute -bottom-6 -right-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-2xl font-bold shadow-xl">
                  <Award className="h-5 w-5 inline mr-2" />
                  ISO 27001 Certified
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section id="why-choose" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900 to-blue-900"></div>
        
        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white mb-8">
              <CheckSquare className="h-5 w-5 mr-2 text-emerald-400" />
              <span className="font-medium">Why Choose Audit AI</span>
            </div>
            
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              <span className="block">The Smart Choice for</span>
              <span className="block bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">
                Modern Auditing
              </span>
            </h2>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Experience the advantages that make us the preferred choice for financial 
              professionals worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Clock,
                title: "Save 80% Time",
                description: "Reduce audit time from weeks to days with our AI-powered automation",
                stat: "80%"
              },
              {
                icon: DollarSign,
                title: "Cut Costs by 60%",
                description: "Significant cost reduction through automated processes and efficiency",
                stat: "60%"
              },
              {
                icon: Shield,
                title: "99.9% Accuracy",
                description: "Industry-leading accuracy rates that exceed human performance",
                stat: "99.9%"
              },
              {
                icon: UserCheck,
                title: "Expert Support",
                description: "24/7 support from financial and technical experts",
                stat: "24/7"
              },
              {
                icon: Lock,
                title: "Bank-Grade Security",
                description: "Military-grade encryption and compliance with global standards",
                stat: "100%"
              },
              {
                icon: Zap,
                title: "Lightning Fast",
                description: "Process thousands of documents in minutes, not hours",
                stat: "10x"
              }
            ].map((item, index) => (
              <div key={index} className="group bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:bg-white/10 transition-all duration-500 transform hover:scale-105">
                <div className="flex items-center justify-between mb-6">
                  <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <item.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-blue-400">{item.stat}</div>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-200 transition-colors">
                  {item.title}
                </h3>
                
                <p className="text-white/70 leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-900 to-indigo-900"></div>
        
        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white mb-8">
              <PhoneCall className="h-5 w-5 mr-2 text-purple-400" />
              <span className="font-medium">Get In Touch</span>
            </div>
            
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              <span className="block">Ready to</span>
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Get Started?
              </span>
            </h2>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Contact our team of experts to learn how Audit AI can transform your financial workflow.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">Send us a message</h3>
              
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white/80 mb-2">First Name</label>
                    <input 
                      type="text" 
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
                      placeholder=""
                    />
                  </div>
                  <div>
                    <label className="block text-white/80 mb-2">Last Name</label>
                    <input 
                      type="text" 
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
                      placeholder=""
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2">Email</label>
                  <input 
                    type="email" 
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
                    // placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2">Company</label>
                  <input 
                    type="text" 
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
                    placeholder="Your Company"
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-2">Message</label>
                  <textarea 
                    rows={4}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
                    placeholder="Tell us about your audit needs..."
                  ></textarea>
                </div>
                
                <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl py-3 font-semibold">
                  <Send className="h-5 w-5 mr-2" />
                  Send Message
                </Button>
              </form>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                <div className="flex items-center gap-4 mb-4">
                  <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <Mail className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white">Email Us</h4>
                    <p className="text-white/70"><EMAIL></p>
                  </div>
                </div>
                <p className="text-white/60">
                  Get in touch with our team for support, sales inquiries, or partnerships.
                </p>
              </div>

              <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                <div className="flex items-center gap-4 mb-4">
                  <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <Phone className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white">Call Us</h4>
                    <p className="text-white/70">(+91) 44 28112985</p>
                  </div>
                </div>
                <p className="text-white/60">
                  Speak directly with our experts. Available Monday-Friday, 9AM-6PM IST.
                </p>
              </div>

              <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                <div className="flex items-center gap-4 mb-4">
                  <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white">Visit Us</h4>
                    <p className="text-white/70">PKF Sridhar & Santhanam LLP</p>
                    <p className="text-white/70">Head Office</p>
                    <p className="text-white/70">KRD GEE GEE Crystal,</p>
                    <p className="text-white/70">7th Floor, 91-92,</p>
                    <p className="text-white/70">Dr. Radhakrishnan Salai,<br />
                      Mylapore, Chennai 600 004.</p>




                  </div>
                </div>
                
              </div>

              <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
                <h4 className="text-lg font-semibold text-white mb-4">Follow Us</h4>
                <div className="flex gap-4">
                  <a href="https://www.facebook.com/PKFSridharSanthanam" className="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center hover:scale-110 transition-transform">
                    <Facebook className="h-6 w-6 text-white" />
                  </a>
                  
                  <a href="https://in.linkedin.com/company/pkf-sridhar-&-santhanam" className="h-12 w-12 bg-blue-700 rounded-xl flex items-center justify-center hover:scale-110 transition-transform">
                    <Linkedin className="h-6 w-6 text-white" />
                  </a>
                 
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-indigo-900 to-slate-900"></div>
        
        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white mb-8">
              <HelpCircle className="h-5 w-5 mr-2 text-indigo-400" />
              <span className="font-medium">Frequently Asked Questions</span>
            </div>
            
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              <span className="block">Got</span>
              <span className="block bg-gradient-to-r from-indigo-400 to-cyan-400 bg-clip-text text-transparent">
                Questions?
              </span>
            </h2>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Find answers to the most common questions about our AI-powered audit platform.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-4">
            {[
              {
                question: "How accurate is your AI analysis?",
                answer: "Our AI maintains a 99.9% accuracy rate, which exceeds human performance in financial document analysis. The system is continuously trained on millions of audit documents and regulatory frameworks."
              },
              {
                question: "Is my financial data secure?",
                answer: "Absolutely. We use bank-grade encryption, maintain SOC 2 Type II compliance, and follow strict data protection protocols. Your data is encrypted both in transit and at rest, and we never share sensitive information."
              },
              {
                question: "How long does the analysis take?",
                answer: "Most audit documents are analyzed within minutes. Complex multi-document audits typically complete within 1-2 hours, compared to weeks of manual review."
              },
              // {
              //   question: "Do you integrate with existing accounting software?",
              //   answer: "Yes, we integrate with all major accounting platforms including QuickBooks, SAP, Oracle, and custom ERP systems through our robust API infrastructure."
              // },
              {
                question: "What types of audit documents can you analyze?",
                answer: "We support all standard audit documents including financial statements, CARO reports, IFC annexures, tax filings, bank statements, and regulatory compliance documents in multiple formats."
              },
              {
                question: "Do you offer training and support?",
                answer: "We provide comprehensive onboarding, training sessions, and 24/7 support. Our team includes both technical experts and certified auditors to assist with any questions."
              },
              {
                question: "What is your pricing model?",
                answer: "We offer flexible pricing based on document volume and features needed. Plans start at $99/month for small firms, with enterprise solutions available for larger organizations."
              },
              {
                question: "Can I try the platform before purchasing?",
                answer: "Yes! We offer a 14-day free trial with full access to all features. No credit card required to start your trial."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl overflow-hidden">
                <button
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-white/10 transition-colors"
                  onClick={() => toggleFaq(index)}
                >
                  <span className="text-lg font-semibold text-white">{faq.question}</span>
                  {openFaq === index ? (
                    <Minus className="h-5 w-5 text-white flex-shrink-0" />
                  ) : (
                    <Plus className="h-5 w-5 text-white flex-shrink-0" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-8 pb-6">
                    <p className="text-white/80 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 to-purple-900"></div>
        
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2359B6F4' fill-opacity='0.4'%3E%3Cpath d='M30 30c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20zm0 0c0 11.046-8.954 20-20 20S-10 41.046-10 30s8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '120px 120px'
          }}></div>
        </div>

        <div className="relative z-10 w-full px-6 md:px-12 lg:px-20">
          <div className="max-w-4xl mx-auto bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-12 text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to transform your audit process?
            </h2>
            
            <p className="text-xl text-white/80 mb-8">
              Get started today and experience the power of AI-driven financial auditing
            </p>
            
            <div className="flex flex-col sm:flex-row justify-center gap-6">              <Link to="/login">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-12 py-6 text-xl font-semibold shadow-2xl transform hover:scale-105 transition-all duration-300"
                >
                  Start Free Trial
                </Button>
              </Link>
              {/* <Link to="/contact">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="bg-white/15 border-white/70 text-white hover:bg-white/30 hover:border-white font-semibold shadow-lg rounded-full px-12 py-6 text-xl backdrop-blur-sm"
                >
                  Contact Sales
                </Button>
              </Link> */}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            {/* Company Info */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
                  P
                </div>
                <span className="text-xl font-bold text-white">PKF Audit AI</span>
              </div>
              
              <p className="text-white/60">
                Transforming financial audit workflows with cutting-edge AI technology.
              </p>
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-blue-400 mt-1" />
                  <span className="text-white/70">
                   
                    
                    <p className="font-semibold">PKF Sridhar & Santhanam LLP</p>
                    <br />
                    <p className="font-semibold">Head Office</p>
                    
                    KRD GEE GEE Crystal,<br />
                    7th Floor, 91-92,<br />
                    Dr. Radhakrishnan Salai,<br />
                    Mylapore, Chennai 600 004.
                  </span>
                </div>
                
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-blue-400" />
                  <a href="mailto:<EMAIL>" className="text-white/70 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </div>
                
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-blue-400" />
                  <span className="text-white/70">(+91) 44 28112985</span>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                
                <a href="https://in.linkedin.com/company/pkf-sridhar-&-santhanam" className="h-10 w-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors">
                  <Linkedin className="h-5 w-5 text-white" />
                </a>
                <a href="https://www.facebook.com/PKFSridharSanthanam" className="h-10 w-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors">
                  <Facebook className="h-5 w-5 text-white" />
                </a>
                
              </div>
            </div>
            
            {/* Quick Links */}
            <div>
              <h3 className="text-xl font-bold text-white mb-6">Quick Links</h3>
              <ul className="space-y-4">
                <li>
                  <a href="#home" className="text-white/60 hover:text-white transition-colors">Home</a>
                </li>
                <li>
                  <a href="#features" className="text-white/60 hover:text-white transition-colors">Features</a>
                </li>
                <li>
                  <a href="#about" className="text-white/60 hover:text-white transition-colors">About Us</a>
                </li>
                <li>
                  <a href="#why-choose" className="text-white/60 hover:text-white transition-colors">Why Choose Us</a>
                </li>
                <li>
                  <a href="#contact" className="text-white/60 hover:text-white transition-colors">Contact</a>
                </li>
                <li>
                  <a href="#faq" className="text-white/60 hover:text-white transition-colors">FAQ</a>
                </li>
              </ul>
            </div>
              {/* Resources */}
            <div>
              <h3 className="text-xl font-bold text-white mb-6">Resources</h3>
              <ul className="space-y-4">
                <li>
                  <a href="/blog" className="text-white/60 hover:text-white transition-colors">Blog</a>
                </li>
                <li>
                  <a href="/resources" className="text-white/60 hover:text-white transition-colors">Resource Center</a>
                </li>
                <li>
                  <a href="/case-studies" className="text-white/60 hover:text-white transition-colors">Case Studies</a>
                </li>
              </ul>
            </div>
            
            {/* Legal */}
            <div>
              <h3 className="text-xl font-bold text-white mb-6">Legal</h3>
              <ul className="space-y-4">
                <li>
                  <a href="/terms" className="text-white/60 hover:text-white transition-colors">Terms of Service</a>
                </li>
                <li>
                  <a href="/privacy" className="text-white/60 hover:text-white transition-colors">Privacy Policy</a>
                </li>
                <li>
                  <a href="/security" className="text-white/60 hover:text-white transition-colors">Security</a>
                </li>
                <li>
                  <a href="/compliance" className="text-white/60 hover:text-white transition-colors">Compliance</a>
                </li>
                <li>
                  <a href="/gdpr" className="text-white/60 hover:text-white transition-colors">GDPR</a>
                </li>
                <li>
                  <a href="/cookies" className="text-white/60 hover:text-white transition-colors">Cookie Policy</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* Bottom Footer */}
        <div className="border-t border-white/10 py-8">
          <div className="w-full px-6 md:px-12 lg:px-20 flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-white/60 text-sm">
              © {new Date().getFullYear()} PKF Audit AI. All rights reserved.
            </div>
            
            <div className="flex items-center gap-6">
              <a href="/terms" className="text-white/60 hover:text-white transition-colors text-sm">Terms</a>
              <a href="/privacy" className="text-white/60 hover:text-white transition-colors text-sm">Privacy</a>
              <a href="/cookies" className="text-white/60 hover:text-white transition-colors text-sm">Cookies</a>
            </div>
          </div>
        </div>
      </footer>

      {/* Scroll to top button */}
      <button 
        className="fixed bottom-8 right-8 h-14 w-14 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 shadow-2xl flex items-center justify-center text-white z-50 hover:scale-110 transition-all duration-300 border-2 border-white/20"
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
      >
        <ChevronRight className="h-6 w-6 transform -rotate-90" />
      </button>

      
    </div>
  );
};

export default Index;