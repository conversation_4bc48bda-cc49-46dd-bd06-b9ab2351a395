{"hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"}, {"key": "Strict-Transport-Security", "value": "max-age=********; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://www.googleapis.com https://generativelanguage.googleapis.com https://checklistaicap.firebaseapp.com https://checklistaicap.web.app https://apis.google.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://accounts.google.com https://login.microsoftonline.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://accounts.google.com https://login.microsoftonline.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://checklistaicap-default-rtdb.firebaseio.com https://firebaseinstallations.googleapis.com https://www.googleapis.com https://generativelanguage.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://firebase.googleapis.com https://accounts.google.com https://login.microsoftonline.com https://graph.microsoft.com; frame-src 'self' https://checklistaicap.firebaseapp.com https://accounts.google.com https://login.microsoftonline.com https://content.googleapis.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self' https://accounts.google.com https://login.microsoftonline.com; object-src 'none'; media-src 'self'; worker-src 'self'; manifest-src 'self'; upgrade-insecure-requests;"}]}, {"source": "/__/firebase/init.json", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-Robots-Tag", "value": "noindex, nofollow"}, {"key": "Allow", "value": "GET"}]}]}}