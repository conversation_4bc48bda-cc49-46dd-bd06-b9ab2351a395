{"rules": {".read": false, ".write": false, "users": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", ".validate": "newData.hasChildren(['email', 'createdAt']) && newData.child('email').isString() && newData.child('createdAt').isNumber()", "email": {".validate": "newData.isString() && newData.val().matches(/^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$/)"}, "displayName": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 100"}, "createdAt": {".validate": "newData.isNumber() && (!data.exists() || data.val() === newData.val())"}, "lastLoginAt": {".validate": "newData.isNumber()"}}}, "documents": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", "$documentId": {".read": "auth != null && (data.parent().parent().key === auth.uid || $uid === auth.uid)", ".write": "auth != null && $uid === auth.uid && (!data.exists() || data.child('userId').val() === auth.uid) && (!newData.exists() || newData.child('userId').val() === auth.uid)", ".validate": "!newData.exists() || (newData.hasChildren(['name', 'type', 'uploadedAt', 'status']) && newData.child('userId').val() === auth.uid)", "name": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 255"}, "type": {".validate": "newData.isString() && newData.val().matches(/^(pdf|excel|csv|image)$/)"}, "url": {".validate": "newData.isString()"}, "path": {".validate": "newData.isString()"}, "uploadedAt": {".validate": "newData.isNumber()"}, "status": {".validate": "newData.isString() && newData.val().matches(/^(uploading|uploaded|processing|completed|error)$/)"}, "userId": {".validate": "newData.val() === auth.uid"}, "size": {".validate": "newData.isNumber() && newData.val() > 0"}, "checksum": {".validate": "newData.isString()"}, "analysisId": {".validate": "newData.isString()"}}}}, "analyses": {".read": false, ".write": false, ".indexOn": ["userId"], "$analysisId": {".read": "auth != null && data.child('userId').val() === auth.uid", ".write": "auth != null && (!data.exists() || data.child('userId').val() === auth.uid) && newData.child('userId').val() === auth.uid", ".validate": "newData.hasChildren(['userId', 'timestamp', 'companyName']) && newData.child('userId').val() === auth.uid", "userId": {".validate": "newData.val() === auth.uid"}, "companyName": {".validate": "newData.isString() && newData.val().length > 0"}, "timestamp": {".validate": "newData.isNumber()"}, "parameters": {".validate": "newData.exists()"}, "results": {".validate": "newData.exists()"}, "documents": {".validate": "newData.exists()"}, "summary": {".validate": "newData.exists()"}}}, "companies": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", "$companyId": {".validate": "newData.hasChildren(['name', 'createdAt']) && newData.child('userId').val() === auth.uid", "name": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 200"}, "industry": {".validate": "newData.isString() && newData.val().length <= 100"}, "registrationNumber": {".validate": "newData.isString() && newData.val().length <= 50"}, "address": {".validate": "newData.isString() && newData.val().length <= 500"}, "contactEmail": {".validate": "newData.isString() && newData.val().matches(/^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$/)"}, "phone": {".validate": "newData.isString() && newData.val().length <= 20"}, "createdAt": {".validate": "newData.isNumber()"}, "updatedAt": {".validate": "newData.isNumber()"}, "userId": {".validate": "newData.val() === auth.uid"}}}}, "partners": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", "$partnerId": {".validate": "newData.hasChildren(['name', 'type', 'createdAt']) && newData.child('userId').val() === auth.uid", "name": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 200"}, "type": {".validate": "newData.isString() && newData.val().matches(/^(individual|company|subsidiary|associate|joint_venture)$/)"}, "relationship": {".validate": "newData.isString() && newData.val().length <= 100"}, "registrationNumber": {".validate": "newData.isString() && newData.val().length <= 50"}, "address": {".validate": "newData.isString() && newData.val().length <= 500"}, "contactDetails": {".validate": "newData.exists()"}, "createdAt": {".validate": "newData.isNumber()"}, "updatedAt": {".validate": "newData.isNumber()"}, "userId": {".validate": "newData.val() === auth.uid"}}}}, "firm_settings": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", "firmName": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 200"}, "registrationNumber": {".validate": "newData.isString() && newData.val().length <= 50"}, "address": {".validate": "newData.isString() && newData.val().length <= 500"}, "contactEmail": {".validate": "newData.isString() && newData.val().matches(/^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$/)"}, "phone": {".validate": "newData.isString() && newData.val().length <= 20"}, "preferences": {".validate": "newData.exists()"}, "updatedAt": {".validate": "newData.isNumber()"}}}, "audit_history": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", "$historyId": {".validate": "newData.hasChildren(['action', 'timestamp', 'userId']) && newData.child('userId').val() === auth.uid", "action": {".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 100"}, "timestamp": {".validate": "newData.isNumber()"}, "details": {".validate": "newData.exists()"}, "ipAddress": {".validate": "newData.isString() && newData.val().length <= 45"}, "userAgent": {".validate": "newData.isString() && newData.val().length <= 500"}, "userId": {".validate": "newData.val() === auth.uid"}}}}, "user_sessions": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid && auth != null", "$sessionId": {".validate": "newData.hasChildren(['startTime', 'lastActivity']) && newData.child('userId').val() === auth.uid", "startTime": {".validate": "newData.isNumber()"}, "lastActivity": {".validate": "newData.isNumber()"}, "ipAddress": {".validate": "newData.isString() && newData.val().length <= 45"}, "userAgent": {".validate": "newData.isString() && newData.val().length <= 500"}, "userId": {".validate": "newData.val() === auth.uid"}, "active": {".validate": "newData.isBoolean()"}}}}, "app_settings": {".read": "auth != null", ".write": false, "version": {".validate": "newData.isString()"}, "maintenance": {".validate": "newData.isBoolean()"}, "features": {".validate": "newData.exists()"}}}}