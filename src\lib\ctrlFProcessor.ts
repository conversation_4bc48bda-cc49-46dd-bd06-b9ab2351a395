// ctrlFProcessor.ts - Ultra-Robust Pattern Search: "financial statements" + "Standalone/Consolidated financial statements"

import { CheckResult } from './checkDefinitions';

/**
 * Extract text content from PDF using Gemini AI
 */
async function extractTextFromPDFWithGemini(pdfFile: File): Promise<string> {
  try {
    console.log(`📄 Starting Gemini PDF text extraction for file: ${pdfFile.name}`);

    // Convert PDF to base64
    const arrayBuffer = await pdfFile.arrayBuffer();
    const base64String = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

    console.log(`📄 PDF converted to base64: ${base64String.length} characters`);

    // Get Gemini API key from environment
    const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
    
    if (!geminiApiKey) {
      throw new Error('Gemini API key not found. Please set VITE_GEMINI_API_KEY in your environment.');
    }

    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: "Please extract ALL text content from this PDF document. Return only the extracted text without any commentary, analysis, or formatting. Just provide the raw text content as it appears in the document."
            },
            {
              inline_data: {
                mime_type: "application/pdf",
                data: base64String
              }
            }
          ]
        }
      ],
      generation_config: {
        temperature: 0,
        max_output_tokens: 8192
      }
    };

    console.log(`📄 Sending PDF to Gemini for text extraction...`);

    // Make request to Gemini API
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini API Error Response:', errorText);
      throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('No text content returned from Gemini');
    }

    const extractedText = data.candidates[0].content.parts[0].text;
    
    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('Empty text extracted from PDF');
    }

    console.log(`✅ Gemini text extraction complete: ${extractedText.length} characters`);
    console.log(`📄 Preview: "${extractedText.substring(0, 200)}..."`);

    return extractedText.trim();

  } catch (error) {
    console.error('❌ Gemini PDF extraction failed:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        throw new Error('Gemini API key is invalid or missing. Please check your API key configuration.');
      } else if (error.message.includes('quota')) {
        throw new Error('Gemini API quota exceeded. Please try again later.');
      } else if (error.message.includes('network')) {
        throw new Error('Network error accessing Gemini API. Please check your internet connection.');
      }
    }
    
    throw error;
  }
}

/**
 * Ultra-robust count instances function that matches terms exactly like browser Ctrl+F
 * This handles all edge cases including spacing, line breaks, punctuation, and formatting
 */
function countInstances(text: string, searchTerm: string): {
  count: number;
  matches: string[];
  matchedContexts: string[];
} {
  // Normalize text by collapsing multiple whitespace characters and cleaning up
  const normalizedText = text
    .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
    .replace(/\r\n|\r|\n/g, ' ')  // Replace line breaks with spaces
    .toLowerCase();
  
  const normalizedSearchTerm = searchTerm.toLowerCase().trim();
  
  const matches: string[] = [];
  const matchedContexts: string[] = [];
  
  // Method 1: Direct substring search (most reliable, matches browser Ctrl+F exactly)
  let startIndex = 0;
  while (true) {
    const index = normalizedText.indexOf(normalizedSearchTerm, startIndex);
    if (index === -1) break;
    
    // Extract the actual match from original text
    const originalMatch = text.substring(index, index + normalizedSearchTerm.length);
    matches.push(originalMatch);
    
    // Get context around the match (80 characters before and after for better visibility)
    const contextStart = Math.max(0, index - 80);
    const contextEnd = Math.min(text.length, index + normalizedSearchTerm.length + 80);
    const context = text.substring(contextStart, contextEnd)
      .replace(/\s+/g, ' ')
      .trim();
    matchedContexts.push(`...${context}...`);
    
    startIndex = index + 1; // Move by 1 to catch overlapping matches
  }
  
  console.log(`🔍 Ultra-robust search for "${searchTerm}":`);
  console.log(`  - Total matches found: ${matches.length}`);
  
  matches.forEach((match, index) => {
    console.log(`  - Match ${index + 1}: "${match}" in context: ${matchedContexts[index]}`);
  });
  
  return {
    count: matches.length,
    matches: matches,
    matchedContexts: matchedContexts
  };
}

/**
 * Multiple search methods for comprehensive comparison
 */
function countInstancesMultipleMethods(text: string, searchTerm: string): {
  method1_directSubstring: number;
  method2_normalizedText: number;
  method3_regexWordBoundary: number;
  method4_overlappingSearch: number;
  matches: string[];
  contexts: string[];
  detailedResults: any;
} {
  const normalizedSearchTerm = searchTerm.toLowerCase().trim();
  
  // Method 1: Direct substring search on lowercase text
  const lowerText = text.toLowerCase();
  let method1Count = 0;
  let index = 0;
  while ((index = lowerText.indexOf(normalizedSearchTerm, index)) !== -1) {
    method1Count++;
    index++;
  }
  
  // Method 2: Normalized text (collapse whitespace)
  const normalizedText = text.replace(/\s+/g, ' ').toLowerCase();
  let method2Count = 0;
  index = 0;
  while ((index = normalizedText.indexOf(normalizedSearchTerm, index)) !== -1) {
    method2Count++;
    index++;
  }
  
  // Method 3: Regex with word boundaries
  const escapedTerm = normalizedSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regexPattern = new RegExp(escapedTerm, 'gi');
  const method3Matches = text.match(regexPattern) || [];
  const method3Count = method3Matches.length;
  
  // Method 4: Overlapping search with detailed tracking (most comprehensive)
  const matches: string[] = [];
  const contexts: string[] = [];
  let method4Count = 0;
  
  index = 0;
  while ((index = lowerText.indexOf(normalizedSearchTerm, index)) !== -1) {
    method4Count++;
    
    // Get the actual text from original (preserving case)
    const actualMatch = text.substring(index, index + normalizedSearchTerm.length);
    matches.push(actualMatch);
    
    // Get extended context
    const contextStart = Math.max(0, index - 100);
    const contextEnd = Math.min(text.length, index + normalizedSearchTerm.length + 100);
    const context = text.substring(contextStart, contextEnd)
      .replace(/\s+/g, ' ')
      .trim();
    contexts.push(`...${context}...`);
    
    index++;
  }
  
  console.log(`🔍 Multiple search methods for "${searchTerm}":`);
  console.log(`  - Method 1 (Direct substring): ${method1Count}`);
  console.log(`  - Method 2 (Normalized text): ${method2Count}`);  
  console.log(`  - Method 3 (Regex): ${method3Count}`);
  console.log(`  - Method 4 (Overlapping): ${method4Count}`);
  console.log(`  - Browser Ctrl+F should match Method 4: ${method4Count}`);
  
  return {
    method1_directSubstring: method1Count,
    method2_normalizedText: method2Count,
    method3_regexWordBoundary: method3Count,
    method4_overlappingSearch: method4Count,
    matches: matches,
    contexts: contexts,
    detailedResults: {
      searchTerm: normalizedSearchTerm,
      textLength: text.length,
      normalizedTextLength: normalizedText.length,
      regexMatches: method3Matches
    }
  };
}

/**
 * Simple Ctrl+F search for single term
 */
export async function processCtrlFSearch(
  pdfFile: File,
  searchTerm: string
): Promise<CheckResult> {
  try {
    console.log(`🔍 Ctrl+F Search for: "${searchTerm}"`);

    if (!searchTerm?.trim()) {
      return {
        isCompliant: false,
        explanation: 'Search term is required',
        confidence: 0,
        extractedData: {
          searchTerm: '',
          instanceCount: 0,
          error: 'No search term provided'
        }
      };
    }

    // Extract text using Gemini
    const text = await extractTextFromPDFWithGemini(pdfFile);
    
    if (!text?.trim()) {
      return {
        isCompliant: false,
        explanation: 'Could not extract text from PDF file',
        confidence: 0,
        extractedData: {
          searchTerm,
          instanceCount: 0,
          error: 'PDF text extraction failed'
        }
      };
    }

    // Count instances using ultra-robust method
    const result = countInstances(text, searchTerm);
    // Also get multiple methods for comparison
    const multiMethodResult = countInstancesMultipleMethods(text, searchTerm);

    console.log(`📊 Ultra-robust search found ${result.count} instances of "${searchTerm}"`);
    console.log(`📊 Multi-method comparison:`, multiMethodResult);

    return {
      isCompliant: result.count > 0,
      explanation: result.count > 0 
        ? `Found ${result.count} instances of "${searchTerm}"`
        : `No instances found for "${searchTerm}"`,
      confidence: 0.9,
      extractedData: {
        // Core Information
        searchTerm,
        instanceCount: result.count,
        textLength: text.length,
        extractionMethod: 'Gemini AI',
        
        // Search Summary
        searchSummary: {
          matchCount: result.count,
          //matchMethod: 'Ultra-robust (Browser Ctrl+F equivalent)',
          searchSuccessful: result.count > 0
        }
      }
    };

  } catch (error) {
    console.error('Error in Ctrl+F search:', error);
    return {
      isCompliant: false,
      explanation: `Error during search: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.3,
      extractedData: {
        searchTerm,
        instanceCount: 0,
        error: error instanceof Error ? error.message : String(error)
      }
    };
  }
}

/**
 * Check Financial Statement type with DUAL PATTERN search and EQUALITY COMPLIANCE CHECK
 * Searches for both "financial statements" AND "Standalone/Consolidated financial statements"
 * COMPLIANCE RULE: Only compliant if BOTH patterns are found AND specific pattern count equals base pattern count
 */
export async function checkFinancialStatementType(
  pdfFile: File,
  expectedType: 'Standalone' | 'Consolidated'
): Promise<CheckResult> {
  try {
    console.log(`🔍 Checking for financial statements with dual pattern search and equality compliance`);
    console.log(`📋 Pattern 1: "financial statements"`);
    console.log(`📋 Pattern 2: "${expectedType} financial statements"`);
    console.log(`⚖️ COMPLIANCE RULE: Both patterns must be found AND counts must be equal`);

    // Extract text using Gemini AI
    const text = await extractTextFromPDFWithGemini(pdfFile);
    
    if (!text?.trim()) {
      return {
        isCompliant: false,
        explanation: 'Could not extract text from PDF file using Gemini',
        confidence: 0,
        extractedData: {
          expectedType,
          error: 'Gemini text extraction failed',
          instanceCount: 0
        }
      };
    }

    console.log(`📄 Gemini extracted ${text.length} characters from PDF`);

    // Define the two search patterns
    const basePattern = "financial statements";
    const specificPattern = `${expectedType} financial statements`;

    console.log(`🔍 Searching for Pattern 1: "${basePattern}"`);
    console.log(`🔍 Searching for Pattern 2: "${specificPattern}"`);

    // Count instances for both patterns using ultra-robust method
    const baseResult = countInstances(text, basePattern);
    const specificResult = countInstances(text, specificPattern);

    // Also get multi-method comparison for both patterns
    const baseMultiMethod = countInstancesMultipleMethods(text, basePattern);
    const specificMultiMethod = countInstancesMultipleMethods(text, specificPattern);

    console.log(`📊 Dual Pattern Search Results (Ultra-Robust):`);
    console.log(`  - "${basePattern}": ${baseResult.count} instances`);
    console.log(`  - "${specificPattern}": ${specificResult.count} instances`);
    console.log(`  - Base pattern multi-method:`, baseMultiMethod);
    console.log(`  - Specific pattern multi-method:`, specificMultiMethod);
    console.log(`  - Text extraction: Gemini AI`);
    //console.log(`  - Search method: Ultra-robust substring matching (browser Ctrl+F equivalent)`);

    // NEW COMPLIANCE LOGIC: Check if both patterns are found AND if counts are equal
    const bothPatternsFound = baseResult.count > 0 && specificResult.count > 0;
    const countsAreEqual = baseResult.count === specificResult.count;
    const isCompliant = bothPatternsFound && countsAreEqual;

    console.log(`⚖️ Compliance Analysis:`);
    console.log(`  - Both patterns found: ${bothPatternsFound}`);
    console.log(`  - Counts are equal: ${countsAreEqual} (${baseResult.count} === ${specificResult.count})`);
    console.log(`  - Final compliance: ${isCompliant}`);

    if (isCompliant) {
      return {
        isCompliant: true,
        explanation: `✅ COMPLIANT: Found ${specificResult.count} instances of "${specificPattern}" and ${baseResult.count} instances of "${basePattern}" - counts are equal!`,
        confidence: 0.95,
        extractedData: {
          // Core Information
          expectedType,
          basePattern,
          specificPattern,
          basePatternCount: baseResult.count,
          specificPatternCount: specificResult.count,
          instanceCount: specificResult.count,
          textLength: text.length,
          extractionMethod: 'Gemini AI',
          
          // Compliance Summary
          complianceRule: 'Both patterns found AND counts must be equal',
          complianceStatus: {
            bothPatternsFound: bothPatternsFound,
            countsAreEqual: countsAreEqual,
            baseCount: baseResult.count,
            specificCount: specificResult.count,
            equalityCheck: `${baseResult.count} === ${specificResult.count} = ${countsAreEqual}`
          },
          
          // Search Summary
          searchSummary: {
            financialStatementsCount: baseResult.count,
            specificFinancialStatementsCount: specificResult.count,
            //matchMethod: 'Ultra-robust (Browser Ctrl+F equivalent)'
          }
        }
      };
    } else {
      // Determine specific reason for non-compliance
      let explanation = `❌ NON-COMPLIANT: `;
      
      if (!bothPatternsFound) {
        if (baseResult.count === 0 && specificResult.count === 0) {
          explanation += `No instances found for either "${basePattern}" or "${specificPattern}"`;
        } else if (baseResult.count === 0) {
          explanation += `No instances found for "${basePattern}" (found ${specificResult.count} instances of "${specificPattern}")`;
        } else if (specificResult.count === 0) {
          explanation += `No instances found for "${specificPattern}" (found ${baseResult.count} instances of "${basePattern}")`;
        }
      } else if (!countsAreEqual) {
        explanation += `Counts are NOT equal: found ${baseResult.count} instances of "${basePattern}" but ${specificResult.count} instances of "${specificPattern}"`;
      }
      
      explanation += ` `;

      return {
        isCompliant: false,
        explanation,
        confidence: 0.95,
        extractedData: {
          // Core Information
          expectedType,
          basePattern,
          specificPattern,
          basePatternCount: baseResult.count,
          specificPatternCount: specificResult.count,
          instanceCount: 0, // Non-compliant
          textLength: text.length,
          extractionMethod: 'Gemini AI',
          
          // Compliance Summary
          complianceRule: 'Both patterns found AND counts must be equal',
          complianceStatus: {
            bothPatternsFound: bothPatternsFound,
            countsAreEqual: countsAreEqual,
            baseCount: baseResult.count,
            specificCount: specificResult.count,
            equalityCheck: `${baseResult.count} === ${specificResult.count} = ${countsAreEqual}`,
            failureReason: !bothPatternsFound ? 'MISSING_PATTERNS' : 'UNEQUAL_COUNTS'
          },
          
          // Search Summary
          searchSummary: {
            financialStatementsCount: baseResult.count,
            specificFinancialStatementsCount: specificResult.count,
            //matchMethod: 'Ultra-robust (Browser Ctrl+F equivalent)'
          }
        }
      };
    }

  } catch (error) {
    console.error('Error in dual pattern financial statement check:', error);
    return {
      isCompliant: false,
      explanation: `Error during dual pattern check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.3,
      extractedData: {
        expectedType,
        error: error instanceof Error ? error.message : String(error),
        extractionMethod: 'Gemini AI (Failed)'
      }
    };
  }
}

/**
 * Search with custom prefix
 */
export async function searchWithPrefix(
  pdfFile: File,
  prefix: string,
  searchTerm: string
): Promise<CheckResult> {
  try {
    const combinedTerm = `${prefix} ${searchTerm}`;
    console.log(`🔍 Prefix search: "${combinedTerm}"`);
    
    return await processCtrlFSearch(pdfFile, combinedTerm);
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error in prefix search: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.3,
      extractedData: {
        prefix,
        searchTerm,
        error: error instanceof Error ? error.message : String(error)
      }
    };
  }
}

/**
 * Get detailed search statistics for both patterns
 */
export async function getFinancialStatementStatistics(
  pdfFile: File,
  expectedType: 'Standalone' | 'Consolidated'
): Promise<{
  success: boolean;
  statistics: {
    totalFinancialStatements: number;
    specificFinancialStatements: number;
    textLength: number;
    extractionMethod: string;
    ultraRobustCounts: {
      totalFinancialStatementsUltraRobust: number;
      specificFinancialStatementsUltraRobust: number;
    };
    multiMethodCounts: {
      totalFinancialStatementsMultiMethod: any;
      specificFinancialStatementsMultiMethod: any;
    };
    complianceAnalysis: {
      bothPatternsFound: boolean;
      countsAreEqual: boolean;
      isCompliant: boolean;
      equalityCheck: string;
    };
  };
  error?: string;
}> {
  try {
    const text = await extractTextFromPDFWithGemini(pdfFile);
    
    const baseResult = countInstances(text, "financial statements");
    const specificResult = countInstances(text, `${expectedType} financial statements`);
    const baseMultiMethod = countInstancesMultipleMethods(text, "financial statements");
    const specificMultiMethod = countInstancesMultipleMethods(text, `${expectedType} financial statements`);

    const bothPatternsFound = baseResult.count > 0 && specificResult.count > 0;
    const countsAreEqual = baseResult.count === specificResult.count;
    const isCompliant = bothPatternsFound && countsAreEqual;

    return {
      success: true,
      statistics: {
        // Core Counts
        totalFinancialStatements: baseResult.count,
        specificFinancialStatements: specificResult.count,
        textLength: text.length,
        extractionMethod: 'Gemini AI',
        
        // Compliance Check
        complianceCheck: {
          bothPatternsFound: bothPatternsFound,
          countsAreEqual: countsAreEqual,
          isCompliant: isCompliant,
          equalityCheck: `${baseResult.count} === ${specificResult.count} = ${countsAreEqual}`
        }
      }
    };
  } catch (error) {
    return {
      success: false,
      statistics: {
        // Core Counts
        totalFinancialStatements: 0,
        specificFinancialStatements: 0,
        textLength: 0,
        extractionMethod: 'Gemini AI (Failed)',
        
        // Compliance Check
        complianceCheck: {
          bothPatternsFound: false,
          countsAreEqual: false,
          isCompliant: false,
          equalityCheck: '0 === 0 = true'
        }
      },
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Debug function to test Gemini PDF extraction
 */
export async function debugGeminiExtraction(pdfFile: File): Promise<{
  success: boolean;
  textLength: number;
  preview: string;
  extractionMethod: string;
  searchComparison?: {
    term: string;
    ultraRobustCount: number;
    multiMethodResults: any;
    matches: string[];
    contexts: string[];
  };
  error?: string;
}> {
  try {
    const text = await extractTextFromPDFWithGemini(pdfFile);
    
    // Test with "financial statements" for comparison
    const testTerm = "financial statements";
    const ultraRobustResult = countInstances(text, testTerm);
    const multiMethodResult = countInstancesMultipleMethods(text, testTerm);
    
    return {
      success: true,
      textLength: text.length,
      preview: text.substring(0, 1000),
      extractionMethod: 'Gemini AI',
      searchComparison: {
        term: testTerm,
        matchCount: ultraRobustResult.count,
        //matchMethod: 'Ultra-robust (Browser Ctrl+F equivalent)',
        searchSuccessful: ultraRobustResult.count > 0
      }
    };
  } catch (error) {
    return {
      success: false,
      textLength: 0,
      preview: '',
      extractionMethod: 'Gemini AI (Failed)',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export async function checkAuditOpinionType(
  pdfFile: File,
  expectedOpinionType: 'Adverse' | 'Qualified' | 'Disclaimer'
): Promise<CheckResult> {
  try {
    console.log(`🔍 Checking for audit opinion with dual pattern search and equality compliance`);
    console.log(`📋 Pattern 1: "opinion"`);
    console.log(`📋 Pattern 2: "${expectedOpinionType} opinion"`);
    console.log(`⚖️ COMPLIANCE RULE: Both patterns must be found AND counts must be equal`);

    // Extract text using Gemini AI
    const text = await extractTextFromPDFWithGemini(pdfFile);
    
    if (!text?.trim()) {
      return {
        isCompliant: false,
        explanation: 'Could not extract text from PDF file using Gemini',
        confidence: 0,
        extractedData: {
          expectedOpinionType,
          error: 'Gemini text extraction failed',
          instanceCount: 0
        }
      };
    }

    console.log(`📄 Gemini extracted ${text.length} characters from PDF`);

    // Define the two search patterns
    const basePattern = "opinion";
    const specificPattern = `${expectedOpinionType} opinion`;

    console.log(`🔍 Searching for Pattern 1: "${basePattern}"`);
    console.log(`🔍 Searching for Pattern 2: "${specificPattern}"`);

    // Count instances for both patterns using ultra-robust method
    const baseResult = countInstances(text, basePattern);
    const specificResult = countInstances(text, specificPattern);

    console.log(`📊 Dual Pattern Search Results (Ultra-Robust):`);
    console.log(`  - "${basePattern}": ${baseResult.count} instances`);
    console.log(`  - "${specificPattern}": ${specificResult.count} instances`);

    // NEW COMPLIANCE LOGIC: Check if both patterns are found AND if counts are equal
    const bothPatternsFound = baseResult.count > 0 && specificResult.count > 0;
    const countsAreEqual = baseResult.count === specificResult.count;
    const isCompliant = bothPatternsFound && countsAreEqual;

    console.log(`⚖️ Compliance Analysis:`);
    console.log(`  - Both patterns found: ${bothPatternsFound}`);
    console.log(`  - Counts are equal: ${countsAreEqual} (${baseResult.count} === ${specificResult.count})`);
    console.log(`  - Final compliance: ${isCompliant}`);

    if (isCompliant) {
      return {
        isCompliant: true,
        explanation: `✅ COMPLIANT: Found ${specificResult.count} instances of "${specificPattern}" and ${baseResult.count} instances of "${basePattern}" - counts are equal!`,
        confidence: 0.95,
        extractedData: {
          // Core Information
          expectedOpinionType,
          basePattern,
          specificPattern,
          basePatternCount: baseResult.count,
          specificPatternCount: specificResult.count,
          instanceCount: specificResult.count,
          textLength: text.length,
          extractionMethod: 'Gemini AI',
          
          // Compliance Summary
          complianceRule: 'Both patterns found AND counts must be equal',
          complianceStatus: {
            bothPatternsFound: bothPatternsFound,
            countsAreEqual: countsAreEqual,
            baseCount: baseResult.count,
            specificCount: specificResult.count,
            equalityCheck: `${baseResult.count} === ${specificResult.count} = ${countsAreEqual}`
          },
          
          // Search Summary
          searchSummary: {
            opinionCount: baseResult.count,
            specificOpinionCount: specificResult.count
          }
        }
      };
    } else {
      // Determine specific reason for non-compliance
      let explanation = `❌ NON-COMPLIANT: `;
      
      if (!bothPatternsFound) {
        if (baseResult.count === 0 && specificResult.count === 0) {
          explanation += `No instances found for either "${basePattern}" or "${specificPattern}"`;
        } else if (baseResult.count === 0) {
          explanation += `No instances found for "${basePattern}" (found ${specificResult.count} instances of "${specificPattern}")`;
        } else if (specificResult.count === 0) {
          explanation += `No instances found for "${specificPattern}" (found ${baseResult.count} instances of "${basePattern}")`;
        }
      } else if (!countsAreEqual) {
        explanation += `Counts are NOT equal: found ${baseResult.count} instances of "${basePattern}" but ${specificResult.count} instances of "${specificPattern}"`;
      }

      return {
        isCompliant: false,
        explanation,
        confidence: 0.95,
        extractedData: {
          // Core Information
          expectedOpinionType,
          basePattern,
          specificPattern,
          basePatternCount: baseResult.count,
          specificPatternCount: specificResult.count,
          instanceCount: 0, // Non-compliant
          textLength: text.length,
          extractionMethod: 'Gemini AI',
          
          // Compliance Summary
          complianceRule: 'Both patterns found AND counts must be equal',
          complianceStatus: {
            bothPatternsFound: bothPatternsFound,
            countsAreEqual: countsAreEqual,
            baseCount: baseResult.count,
            specificCount: specificResult.count,
            equalityCheck: `${baseResult.count} === ${specificResult.count} = ${countsAreEqual}`,
            failureReason: !bothPatternsFound ? 'MISSING_PATTERNS' : 'UNEQUAL_COUNTS'
          },
          
          // Search Summary
          searchSummary: {
            opinionCount: baseResult.count,
            specificOpinionCount: specificResult.count
          }
        }
      };
    }

  } catch (error) {
    console.error('Error in dual pattern audit opinion check:', error);
    return {
      isCompliant: false,
      explanation: `Error during dual pattern opinion check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.3,
      extractedData: {
        expectedOpinionType,
        error: error instanceof Error ? error.message : String(error),
        extractionMethod: 'Gemini AI (Failed)'
      }
    };
  }
}