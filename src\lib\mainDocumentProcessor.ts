// mainDocumentProcessor.ts - Main orchestration for document processing

import { CheckResult, CheckDefinition, getApplicableChecks, INDEPENDENT_CHECKS, INTERLINKED_CHECKS, CONDITIONAL_CHECKS } from './checkDefinitions';
import { processSingleDocumentCheck } from './geminiProcessor';
import {
  processAuditReportCAROInterlink,
  processAuditReportIFCInterlink,
  processBalanceSheetCAROPPE,
  processBalanceSheetCAROIntangible,
  processBalanceSheetCAROInventory,
  processBalanceSheetCAROSecuredBorrowings
} from './interlinkedProcessor';

export interface ProcessingProgress {
  completed: number;
  total: number;
  currentCheck: string;
}

export interface DocumentFiles {
  audit_report?: File;
  annexure_a?: File;
  annexure_b?: File;
  balance_sheet?: File;
  notes?: File;
  pl_notes?: File;
  annual_report?: File;
  secretarial_compliance?: File;
  cfs?: File; // Consolidated Financial Statements
}

export interface AnalysisParameters {
  company_name: string;
  audit_date: Date;
  profit_or_loss: string;
  company_listing_status: string;
  top_1000_or_500: string;
  audit_report_type: string;
  audit_opinion_type: string;
  is_nbfc: string;
  has_internal_auditor?: string;
  has_cost_auditor?: string;
  related_party_note_number?: string;
  has_cfs?: string; // Flag indicating if Consolidated Financial Statements are available
}

/**
 * Process all independent document checks
 */
async function processIndependentChecks(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  applicableChecks: CheckDefinition[],
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Record<string, CheckResult>> {
  const results: Record<string, CheckResult> = {};
  const independentChecks = applicableChecks.filter(check => check.type === 'independent');

  let completed = 0;

  for (const check of independentChecks) {
    try {
      console.log(`Processing independent check: ${check.id}`);

      // Update progress
      if (onProgress) {
        onProgress({
          completed,
          total: applicableChecks.length,
          currentCheck: check.name
        });
      }

      // Get the required document
      const documentType = check.documentTypes[0]; // Independent checks use single document
      const documentFile = documents[documentType as keyof DocumentFiles];

      if (!documentFile) {
        console.warn(`Document ${documentType} not available for check ${check.id}`);
        results[check.id] = {
          isCompliant: false,
          explanation: `Required document ${documentType} not available`,
          confidence: 0
        };
        continue;
      }

      // Process the check
      const result = await processSingleDocumentCheck(
        documentFile,
        check.id,
        check.promptKey,
        parameters
      );

      results[check.id] = result;
      completed++;

    } catch (error) {
      console.error(`Error processing independent check ${check.id}:`, error);
      results[check.id] = {
        isCompliant: false,
        explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
        confidence: 0.5
      };
      completed++;
    }
  }

  return results;
}

/**
 * Process all interlinked document checks
 */
async function processInterlinkedChecks(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  applicableChecks: CheckDefinition[],
  independentResults: Record<string, CheckResult>,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Record<string, CheckResult>> {
  const results: Record<string, CheckResult> = {};
  const interlinkedChecks = applicableChecks.filter(check => check.type === 'interlinked');

  let completed = Object.keys(independentResults).length;

  for (const check of interlinkedChecks) {
    try {
      console.log(`Processing interlinked check: ${check.id}`);

      // Update progress
      if (onProgress) {
        onProgress({
          completed,
          total: applicableChecks.length,
          currentCheck: check.name
        });
      }

      let result: CheckResult;

      // Route to appropriate interlinked processor
      switch (check.id) {
        case 'ar_caro_reference_matching':
          if (documents.audit_report && documents.annexure_a) {
            result = await processAuditReportCAROInterlink(
              documents.audit_report,
              documents.annexure_a,
              parameters
            );
          } else {
            result = {
              isCompliant: false,
              explanation: 'Required documents (Audit Report, CARO) not available',
              confidence: 0
            };
          }
          break;

        case 'ar_ifc_reference_matching':
          if (documents.audit_report && documents.annexure_b) {
            result = await processAuditReportIFCInterlink(
              documents.audit_report,
              documents.annexure_b,
              parameters
            );
          } else {
            result = {
              isCompliant: false,
              explanation: 'Required documents (Audit Report, IFC) not available',
              confidence: 0
            };
          }
          break;

        case 'bs_caro_ppe_check':
          if (documents.balance_sheet && documents.annexure_a) {
            result = await processBalanceSheetCAROPPE(
              documents.balance_sheet,
              documents.annexure_a,
              parameters
            );
          } else {
            result = {
              isCompliant: false,
              explanation: 'Required documents (Balance Sheet, CARO) not available',
              confidence: 0
            };
          }
          break;

        case 'bs_caro_intangible_check':
          if (documents.balance_sheet && documents.annexure_a) {
            result = await processBalanceSheetCAROIntangible(
              documents.balance_sheet,
              documents.annexure_a,
              parameters
            );
          } else {
            result = {
              isCompliant: false,
              explanation: 'Required documents (Balance Sheet, CARO) not available',
              confidence: 0
            };
          }
          break;

        // DISABLED: Replaced by enhanced multi-document check 'inventory_goods_in_transit_check'
        // case 'bs_caro_inventory_check':
        //   if (documents.balance_sheet && documents.annexure_a && documents.notes) {
        //     result = await processBalanceSheetCAROInventory(
        //       documents.balance_sheet,
        //       documents.annexure_a,
        //       documents.notes,
        //       parameters
        //     );
        //     result.source = 'interlinked';
        //   } else {
        //     result = {
        //       isCompliant: false,
        //       explanation: 'Required documents (Balance Sheet, CARO, Notes) not available',
        //       confidence: 0,
        //       source: 'interlinked'
        //     };
        //   }
        //   break;

        case 'bs_caro_secured_borrowings':
          if (documents.balance_sheet && documents.annexure_a && documents.notes) {
            result = await processBalanceSheetCAROSecuredBorrowings(
              documents.balance_sheet,
              documents.annexure_a,
              documents.notes,
              parameters
            );
            result.source = 'interlinked';
          } else {
            result = {
              isCompliant: false,
              explanation: 'Required documents (Balance Sheet, CARO, Notes) not available',
              confidence: 0,
              source: 'interlinked'
            };
          }
          break;

        default:
          // For other interlinked checks, use generic processing
          console.warn(`Generic processing for interlinked check: ${check.id}`);
          result = {
            isCompliant: false,
            explanation: `Interlinked check ${check.id} not yet implemented`,
            confidence: 0
          };
      }

      results[check.id] = result;
      completed++;

    } catch (error) {
      console.error(`Error processing interlinked check ${check.id}:`, error);
      results[check.id] = {
        isCompliant: false,
        explanation: `Error processing interlinked check: ${error instanceof Error ? error.message : String(error)}`,
        confidence: 0.5
      };
      completed++;
    }
  }

  return results;
}

/**
 * Main function to process all documents for analysis
 */
export async function processDocumentsForAnalysis(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Record<string, CheckResult>> {
  try {
    console.log('Starting document analysis with parameters:', parameters);
    console.log('Available documents:', Object.keys(documents));

    // Get available document types
    const availableDocuments = Object.keys(documents).filter(key => documents[key as keyof DocumentFiles]);

    // Get applicable checks based on parameters and available documents
    const applicableChecks = getApplicableChecks(parameters, availableDocuments);
    

    // mainDocumentProcessor.ts (Continued)

    if (applicableChecks.length === 0) {
      throw new Error('No applicable checks found for the given parameters and documents');
    }

    // Initialize progress
    if (onProgress) {
      onProgress({
        completed: 0,
        total: applicableChecks.length,
        currentCheck: 'Starting analysis...'
      });
    }

    // Process independent checks first
    console.log('Processing independent checks...');
    const independentResults = await processIndependentChecks(
      documents,
      parameters,
      applicableChecks,
      onProgress
    );

    // Process interlinked checks
    console.log('Processing interlinked checks...');
    const interlinkedResults = await processInterlinkedChecks(
      documents,
      parameters,
      applicableChecks,
      independentResults,
      onProgress
    );

    // Combine all results
    const allResults = { ...independentResults, ...interlinkedResults };

    // Final progress update
    if (onProgress) {
      onProgress({
        completed: applicableChecks.length,
        total: applicableChecks.length,
        currentCheck: 'Analysis complete'
      });
    }

    console.log(`Analysis complete. Processed ${Object.keys(allResults).length} checks.`);
    return allResults;

  } catch (error) {
    console.error('Error in document analysis:', error);
    throw error;
  }
}

/**
 * Get summary statistics for analysis results
 */
export function getAnalysisSummary(results: Record<string, CheckResult>): {
  total: number;
  compliant: number;
  nonCompliant: number;
  compliancePercentage: number;
  checksByType: Record<string, { compliant: number; total: number }>;
} {
  const total = Object.keys(results).length;
  const compliant = Object.values(results).filter(result => result.isCompliant).length;
  const nonCompliant = total - compliant;
  const compliancePercentage = total > 0 ? Math.round((compliant / total) * 100) : 0;

  // Group by check type
  const checksByType: Record<string, { compliant: number; total: number }> = {};

  // Get check definitions to determine types
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS];

  for (const [checkId, result] of Object.entries(results)) {
    const checkDef = allChecks.find(check => check.id === checkId);
    const type = checkDef?.type || 'unknown';

    if (!checksByType[type]) {
      checksByType[type] = { compliant: 0, total: 0 };
    }

    checksByType[type].total++;
    if (result.isCompliant) {
      checksByType[type].compliant++;
    }
  }

  return {
    total,
    compliant,
    nonCompliant,
    compliancePercentage,
    checksByType
  };
}

/**
 * Validate documents before processing
 */
export function validateDocuments(documents: DocumentFiles): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if at least audit report is provided
  if (!documents.audit_report) {
    errors.push('Audit Report is required for analysis');
  }

  // Check file types
  for (const [docType, file] of Object.entries(documents)) {
    if (file && file.type !== 'application/pdf') {
      errors.push(`${docType} must be a PDF file`);
    }

    if (file && file.size === 0) {
      errors.push(`${docType} file is empty`);
    }

    if (file && file.size > 50 * 1024 * 1024) { // 50MB limit
      warnings.push(`${docType} file is very large (${Math.round(file.size / 1024 / 1024)}MB). Processing may be slow.`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get recommended documents based on parameters
 */
export function getRecommendedDocuments(parameters: AnalysisParameters): {
  required: string[];
  recommended: string[];
  optional: string[];
} {
  const required = ['audit_report'];
  const recommended: string[] = [];
  const optional: string[] = [];

  // CARO is recommended for most checks
  recommended.push('annexure_a');

  // IFC is recommended for internal control checks
  recommended.push('annexure_b');

  // Balance sheet for financial validations
  recommended.push('balance_sheet');

  // Notes for detailed analysis
  optional.push('notes');
  optional.push('pl_notes');

  // Annual report for comprehensive checks
  optional.push('annual_report');
  optional.push('sec_report');


  // Secretarial compliance for listed companies
  if (parameters.company_listing_status === 'Listed') {
    recommended.push('secretarial_compliance');
  }

  return { required, recommended, optional };
}

/**
 * Export functions for external use
 */
export {
  processIndependentChecks,
  processInterlinkedChecks
};