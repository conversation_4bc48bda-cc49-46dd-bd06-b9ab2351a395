
import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BarChart3, FileText, History, Upload, ArrowUpRight, PlusCircle, Clock } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getAnalysisHistory, StoredAnalysisResult } from "@/lib/firebaseStorageService";
import { testFirebaseConnection } from "@/lib/firebaseTest";
import { format } from "date-fns";
import { CheckResult } from "@/lib/checkDefinitions";

const Dashboard = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const { data: analysisHistory, isLoading } = useQuery({
    queryKey: ["analysisHistory", currentUser?.uid],
    queryFn: () => (currentUser ? getAnalysisHistory(currentUser.uid) : Promise.resolve([])),
    enabled: !!currentUser,
  });

  const handleAnalyzerClick = () => {
    navigate("/dashboard/analyzer");
  };
  const handleViewDetails = (id: string) => {
    navigate(`/dashboard/analysis/${id}`);
  };

  const handleTestFirebase = async () => {
    try {
      const result = await testFirebaseConnection();
      console.log('🔥 Firebase Test Result:', result);
      alert(`Firebase Test:\nConnected: ${result.connected}\nHas Analyses: ${result.hasAnalyses}\nCheck console for details`);
    } catch (error) {
      console.error('🔥 Firebase Test Error:', error);
      alert(`Firebase Test Failed: ${error}`);
    }
  };

  // Function to determine status badge color
  const getStatusColor = (compliant: number, total: number) => {
    const ratio = compliant / total;
    if (ratio === 1) return "bg-green-500";
    if (ratio >= 0.75) return "bg-yellow-500";
    return "bg-red-500";
  };
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor your audit analysis progress and insights</p>
        </div>
        {/* <Button onClick={handleTestFirebase} variant="outline" size="sm" className="text-xs">
          Test Firebase
        </Button> */}
      </div>

      {/* Welcome Card with Improved Design */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-600 to-blue-700 text-white overflow-hidden relative">
        <CardContent className="p-6 md:p-8 relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex-1">
              <h2 className="text-xl md:text-2xl font-semibold mb-3">
                Welcome back, {currentUser?.displayName || "User"}
              </h2>
              <p className="text-blue-100 max-w-lg text-sm md:text-base leading-relaxed">
                Let's analyze your audit reports for compliance and disclosures. Upload new documents or review your previous analyses.
              </p>
            </div>
            <Button
              size="lg"
              onClick={handleAnalyzerClick}
              className="bg-white hover:bg-gray-50 text-blue-600 font-medium shadow-md hover:shadow-lg transition-all duration-200 shrink-0"
            >
              <Upload className="mr-2 h-4 w-4" />
              Start New Analysis
            </Button>
          </div>
        </CardContent>
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-white/5 bg-gradient-to-br from-transparent to-black/10"></div>
      </Card>

      {/* Quick Stats with Modern Design */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-white border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Analyses</p>
                <h3 className="text-2xl font-bold text-gray-900">{analysisHistory?.length || 0}</h3>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <BarChart3 className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Documents Analyzed</p>
                <h3 className="text-2xl font-bold text-gray-900">
                  {analysisHistory?.reduce(
                    (total, item) => {
                      // Count documents in the new Firebase structure
                      const documentCount = item.documents ? Object.keys(item.documents).length : 0;
                      return total + documentCount;
                    },
                    0
                  ) || 0}
                </h3>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <FileText className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Recent Analysis</p>
                <h3 className="text-2xl font-bold text-gray-900">
                  {isLoading
                    ? "..."
                    : analysisHistory && analysisHistory.length > 0
                      ? format(new Date(analysisHistory[0].timestamp), "MMM d")
                      : "None"}
                </h3>
              </div>
              <div className="bg-purple-50 p-3 rounded-lg">
                <History className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Analyses with Enhanced Table */}
      <Card className="bg-white border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50/50 border-b border-gray-100 px-6 py-5">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Recent Analyses</CardTitle>
              <CardDescription className="text-sm text-gray-600 mt-1">
                Your most recent audit report analyses
              </CardDescription>
            </div>
            {analysisHistory && analysisHistory.length > 0 && (
              <Button
                variant="outline"
                onClick={() => navigate("/dashboard/history")}
                className="flex items-center text-sm hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-colors"
              >
                View All <ArrowUpRight className="h-4 w-4 ml-1" />
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="text-center py-16 space-y-4">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
              <p className="text-gray-500">Loading your analysis history...</p>
            </div>
          ) : analysisHistory && analysisHistory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Type</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">                  {analysisHistory.slice(0, 5).map((analysis) => {
                    // Use the stored summary instead of recalculating
                    const summary = analysis.summary || { total: 0, compliant: 0, nonCompliant: 0, compliancePercentage: 0 };

                    return (
                      <tr key={analysis.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{analysis.companyName}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {format(new Date(analysis.timestamp), "h:mm a")}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {format(new Date(analysis.timestamp), "MMM d, yyyy")}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            {analysis.parameters.audit_report_type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div
                              className={`h-2.5 w-2.5 rounded-full mr-2 ${getStatusColor(summary.compliant, summary.total)}`}
                            ></div>
                            <span className="text-sm">
                              {summary.compliant}/{summary.total} checks passed
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(analysis.id)}
                            className="hover:bg-gray-100 hover:text-gray-900"
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-16 space-y-4">
              <div className="bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto text-gray-400">
                <PlusCircle className="h-8 w-8" />
              </div>
              <div>
                <p className="text-lg font-medium text-gray-900">No analysis history found</p>
                <p className="text-gray-500 mt-1">Start by analyzing an audit report.</p>
              </div>
              <Button
                onClick={handleAnalyzerClick}
                className="mt-2 bg-primary hover:bg-primary/90"
              >
                <Upload className="mr-2 h-4 w-4" />
                New Analysis
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
