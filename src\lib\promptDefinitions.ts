import { be } from "date-fns/locale";
import { addMultiDocumentPrompts } from './multiDocumentPrompts';

export interface PromptTemplate {
  key: string;
  template: string;
  variables?: string[];
}

// Base single-document prompt templates
const BASE_PROMPT_TEMPLATES: Record<string, PromptTemplate> = {
  // Independent Document Checks

  // Stage-1

  // Stage 1.1
  audit_title: {
    key: 'audit_title',
    template: `
      Analyze this audit report PDF. Check if there is EXACTLY the heading "Independent Auditor's Report" or "Independent Auditors' Report".
      
      Return ONLY:
      - First line: "Yes" or "No" (indicating if the exact heading is found)
      
      - Additional explanation of what you found
    `,
    variables: []
  },
  // Stage 1.2
  company_format: {
    key: 'company_format',
    template: `
      Analyze this audit report PDF. Check if there is EXACTLY the text "To the Members of {{company_name}}"
      below the Title and above the Opinion Section. It should match word-for-word, including spaces.

      Return ONLY:
      - First line: "Yes" or "No" (indicating if the exact text is found)
      - Additional explanation of what you found or why it doesn't match
    `,
    variables: ['company_name']
  },

  // Stage 1.3

  // REPLACE the existing signature_date prompt in promptDefinitions.ts with this enhanced version:

signature_date: {
  key: 'signature_date',
  template: `
    Analyze this audit report PDF to check PKF Sridhar & Santhanam LLP signature block for company name and date verification.

    **SIMPLE TASK:** 
    1. Find the PKF signature block in the audit report
    2. Extract the company name and audit date
    3. Compare with user inputs

    **USER INPUTS TO VERIFY:**
    - Company Name: {{company_name}}
    - Audit Date: {{audit_date}}

    **WHAT TO EXTRACT:**
    1. Look for "For PKF Sridhar & Santhanam LLP" signature block
    2. Find the company name mentioned in the audit report
    3. Find the date mentioned in the signature block

    **SIMPLE COMPARISON:**
    - Does the company name in document match user input "{{company_name}}"?
    - Does the date in signature block match user input date?

    **RESPONSE FORMAT (KEEP IT SIMPLE):**

    First line: "Yes" if both company name and date match, "No" if either doesn't match

    SIGNATURE VERIFICATION:
    Company Name Check:
    - User Input: {{company_name}}
    - Document Shows: [exact company name found]
    - Match: [Yes/No]

    Date Check:
    - User Input: {{audit_date}}
    - Document Shows: [exact date found]
    - Match: [Yes/No]

    FINAL RESULT: [Compliant/Non-Compliant]

    EXPLANATION: [Brief 1-2 line explanation of what matched or didn't match]

    **EXAMPLE:**

    **If Both Match:**
    Yes
    SIGNATURE VERIFICATION:
    Company Name Check:
    - User Input: PKF
    - Document Shows: PKF
    - Match: Yes

    Date Check:
    - User Input: 2024-05-11
    - Document Shows: 11th May 2024
    - Match: Yes

    FINAL RESULT: Compliant
    EXPLANATION: Both company name and audit date match between user input and document.

    **If Name or Date Doesn't Match:**
    No
    SIGNATURE VERIFICATION:
    Company Name Check:
    - User Input: ABC Company
    - Document Shows: PKF
    - Match: No

    Date Check:
    - User Input: 2024-05-11
    - Document Shows: 12th May 2024
    - Match: No

    FINAL RESULT: Non-Compliant
    EXPLANATION: Company name and/or date don't match between user input and document.
  `,
  variables: ['company_name', 'audit_date']
},



 // Stage 1.4
  financial_statements_type: {
    key: 'financial_statements_type',
    template: `
      I need you to perform a search for all instances of "financial statements" in this document:

      1. Find every instance of the phrase "financial statements" in the document (case-insensitive)
      2. For each instance, check whether "{{report_type}}" directly precedes it
      3. Count how many instances have the proper qualifier and how many don't
      4. For instances missing the qualifier, extract the context (5-10 words before and after)

      Return:
      - First line: "Yes" if ALL instances are properly qualified, "No" if ANY are not
      - Second line: "Found X out of Y instances properly qualified with '{{report_type}}'"
      - For unqualified instances, list the context with the phrase "financial statements"
      - Brief explanation of your assessment
    `,
    variables: ['report_type']
  },
// Stage 1.5
  brsr_brr: {
    key: 'brsr_brr',
    template: `
      You are analyzing an audit report PDF. The company listing status is {{company_listing_status}} and top 1000/500 status is {{top_1000_or_500}}.
      
      {{#if_top_1000_500}}
      Therefore, we expect "Business Responsibility and Sustainability Report (BRSR)" — NOT plain "business responsibility report".
      
      STRICT REQUIREMENTS:
      1) Look specifically in the 'Information Other than the financial statements' section (case-insensitive search).
      2) If you find either:
         - The exact phrase "Business Responsibility and Sustainability Report"
         - OR the acronym "BRSR"
         respond with:
            - First line: "Yes"
            - Second line: A brief snippet or explanation
      3) If you instead find "business responsibility report" (BRR) without "sustainability",
         OR you do not find BRSR at all, respond:
            - First line: "No"
            - Second line: Explanation
      {{else}}
      Therefore, we expect the EXACT phrase "business responsibility report" (BRR) — NOT BRSR.
      
      STRICT REQUIREMENTS:
      1) Look specifically in the 'Information Other than the financial statements' section (case-insensitive search).
      2) If you find "Business Responsibility and Sustainability Report" or "BRSR" anywhere, respond:
            - First line: "No"
            - Second line: Explanation (because that's not acceptable for this scenario)
      3) If you find the exact phrase "business responsibility report" (three words consecutively),
         respond "Yes" plus a brief snippet.
      4) If you do NOT find "business responsibility report," respond "No" with an explanation.
      {{/if_top_1000_500}}

      Return ONLY:
      - The first line: "Yes" or "No"
      - The second line: short reason.
    `,
    variables: ['company_listing_status', 'top_1000_or_500']
  },
// Stage 1.6
  key_audit_matter: {
    key: 'key_audit_matter',
    template: `
      Analyze this audit report PDF to check for Key Audit Matters (KAM) compliance.

      **TASK:** Verify that the "Key Audit Matters" section contains at least one substantive audit matter presented in table format or structured format.

      **SPECIFIC REQUIREMENTS:**
      1. First, locate the "Key Audit Matters" or "KAM" section in the audit report
      2. Check if this section contains actual audit matters (not just headings or disclaimers)
      3. Verify that at least ONE specific audit matter is presented with:
         - Matter description/identification
         - How the matter was addressed in the audit
         - Proper structured presentation (table format or equivalent structured layout)

      **COMPLIANCE CRITERIA:**
      - COMPLIANT: Key Audit Matters section exists AND contains at least one substantive audit matter in structured/table format
      - NON-COMPLIANT: Key Audit Matters section is missing, OR exists but contains no substantive matters, OR matters are not properly structured

      **WHAT MAKES IT COMPLIANT:**
      ✅ Key Audit Matters section with at least one detailed audit matter in table/structured format
      ✅ Each matter includes description and audit approach
      ✅ Matters are presented in organized, tabular, or clearly structured format

      **WHAT MAKES IT NON-COMPLIANT:**
      ❌ No Key Audit Matters section found
      ❌ Key Audit Matters section exists but is empty or contains only generic text
      ❌ No substantive audit matters presented
      ❌ Audit matters not presented in proper structured/table format

      **ANALYSIS PROCESS:**
      1. Search for "Key Audit Matters", "Key Audit Matter", or "KAM" section
      2. Extract the content of this section
      3. Count the number of substantive audit matters presented
      4. Verify each matter is presented in structured format (table, columns, or clear organization)
      5. Check that each matter includes both description and audit approach

      Return STRICTLY in this format:
      - First line: "Yes" if Key Audit Matters section contains at least one properly structured audit matter, "No" otherwise
      - Second line: "KAM Section: [Found/Missing], Structured Matters Count: [Number]"
      - Third line: "Matter Details: [Brief description of the matters found or reason for non-compliance]"
      - Fourth line: "Compliance Status: [Brief explanation of compliance/non-compliance]"
      - Fifth line: "Recommendation: [Professional high-level explanation and guidance]"

      **EXAMPLE RESPONSES:**

      **If Compliant:**
      Yes
      KAM Section: Found, Structured Matters Count: 2
      Matter Details: Found revenue recognition matter and inventory valuation matter, both presented in structured table format with descriptions and audit procedures.
      Compliance Status: Full compliance - Key Audit Matters section contains substantive matters in proper structured format.
      Recommendation: This Key Audit Matters section demonstrates excellent compliance with SA 701 requirements by presenting substantive audit matters in clear, structured format. The inclusion of specific audit matters with detailed descriptions and corresponding audit procedures enhances audit transparency and provides valuable insights to stakeholders about significant audit considerations. This approach strengthens stakeholder confidence by highlighting the auditor's focus areas and demonstrating thorough professional judgment in identifying and addressing key audit risks.

      **If Non-Compliant - No Section:**
      No
      KAM Section: Missing, Structured Matters Count: 0
      Matter Details: No Key Audit Matters section found in the audit report.
      Compliance Status: Non-compliant due to missing Key Audit Matters section entirely.
      Recommendation: The audit report lacks the mandatory Key Audit Matters section required under SA 701 for listed company audits. This section is critical for communicating matters that required significant auditor attention during the audit. The audit firm must include a comprehensive Key Audit Matters section with at least one substantive audit matter presented in structured format, including matter descriptions and audit procedures undertaken. This addition will ensure compliance with auditing standards and enhance audit quality and transparency.

      **If Non-Compliant - Empty Section:**
      No
      KAM Section: Found, Structured Matters Count: 0
      Matter Details: Key Audit Matters section exists but contains no substantive audit matters or only generic disclaimers.
      Compliance Status: Non-compliant - KAM section present but lacks required substantive content.
      Recommendation: While the Key Audit Matters section heading exists, it fails to fulfill the substantive requirements of SA 701 by not presenting any specific audit matters in structured format. The section must include at least one significant matter that required substantial auditor attention, presented with clear descriptions and corresponding audit procedures. The audit firm should identify and document key areas of audit focus such as significant accounting estimates, complex transactions, or areas of significant management judgment, presenting them in proper table or structured format to meet compliance requirements.
    `,
    variables: []
  },
  // Stage 1.7
  audit_trail_software: {
    key: 'audit_trail_software',
    template: `
      Analyze this audit report PDF to check if it mentions that the company uses an accounting software
      that has a feature of recording audit trail (audit log).

      Look for phrases like:
      - "accounting software which has a feature of recording audit trail"
      - "accounting software for maintaining its books of account which has a feature of recording audit trail"
      - "books of account are maintained in an accounting software which has a feature to record audit trail"
      - Any similar phrasing that indicates the company uses accounting software with audit trail capabilities

      Return ONLY:
      - First line: "Yes" or "No" (indicating if such accounting software is mentioned)
      - Then explain what you found, including the exact text extract where this is mentioned
      - Recommendation : if this point is compliant needs to give the recommendation with Hilevel Explaination in a Professional way 

    `,
    variables: []
  },
// Stage 1.8
  section_197_reference: {
    key: 'section_197_reference',
    template: `
      TASK: Check if the audit report contains a specific reference to Section 197(16) of the Companies Act, 2013.

      SPECIFIC INSTRUCTIONS:
      1. Search the entire audit report for any mention of "Section 197(16)" or "Section 197 (16)" of the Companies Act.

      2. The reference should specifically include BOTH:
         - The section number "197" AND
         - The subsection "(16)" or " (16)"

      3. This reference is typically found in the "Report on Other Legal and Regulatory Requirements" section
         and relates to director remuneration disclosures.

      4. A compliant report must explicitly mention Section 197(16) in relation to director remuneration.

      PROVIDE EXACTLY:
      - First line: "Yes" if Section 197(16) is explicitly mentioned, "No" if it is not
      - Second line: The exact quote containing the Section 197(16) reference (if found)
      - Additional explanation of what you found, including context
      - Recommendation : if this point is compliant needs to give the recommendation with Hilevel Explaination in a Professional way 

    `,
    variables: []
  },
// Stage 1.9
  company_name_consistency: {
    key: 'company_name_consistency',
    template: `
      Analyze this audit report PDF to check if the company name "{{company_name}}" is used consistently throughout the document.

      1. Identify all instances where the base name appears as part of a company name.
      2. Compare these mentions against the expected full name "{{company_name}}".
      3. Check for variations, different subsidiary names, or inconsistent usage.
      4. Generate a list of all variations found and categorize them as follows:
         - If the name appears EXACTLY as "{{company_name}}" mark it as "Compliant"
         - If the name appears with DIFFERENT wording (missing words, extra words, different order) mark it as "Non-Compliant"
         - For each non-compliant variation, provide the exact text and the context where it was found.

      Return:
      - First line: "Yes" or "No" (indicating if the company name is consistent)
      - Second line: The total count of company name mentions
      - Third line: The number of variations found
      - Then list ALL non-compliant variations found, formatted like:
        "VARIATION: [exact text found]"
        "CONTEXT: [surrounding sentence or paragraph]"
      - Finally, briefly summarize the issues found
    `,
    variables: ['company_name']
  },
// Stage 1.11
  consolidated_wording: {
    key: 'consolidated_wording',
    template: `
      Analyze this audit report PDF to check for consistent use of "consolidated" throughout the document.
      
      1. Find all instances of "financial statements" in the document
      2. Check if "consolidated" precedes each instance
      3. Look for any instances where "standalone" appears instead of "consolidated"
      
      Return:
      - First line: "Yes" if all instances consistently use "consolidated", "No" if any inconsistencies found
      - Second line: Summary of findings with count of consistent vs inconsistent uses

    `,
    variables: []
  },
// Stage 1.12
  caro_nbfc_iii_clause_present: {
    key: 'caro_nbfc_iii_clause_present',
    template: `
      Scan this CARO Annexure for the sub‑clause labels "(iii)(a)" or "(iii)(e)".
      For NBFCs, these clauses should be correctly omitted or marked as not applicable.
      
      Return STRICTLY:
      Line 1 → "Yes" if clauses are properly handled for NBFC, else "No"
      Line 2 → Explanation of what was found regarding clauses (iii)(a) and (iii)(e)
      - Recommendation : if this point is compliant needs to give the recommendation with Hilevel Explaination in a Professional way 

    `,
    variables: []
  },

  // Stage 1.13

  // CARO Clause Verification Prompts

clause_20: {
  key: 'clause_20',
  template: `
    You are an AI auditor assistant analyzing a CARO (Companies Auditor's Report Order) Annexure document for STANDALONE Financial Statements. Read the given auditor's report annexure under the title "Annexure A" which refers to the 'Report on Other Legal and Regulatory Requirements' for standalone financial statements.

    **TASK:** Verify that the document contains exactly 20 clauses numbered with Roman numerals from (i) to (xx) as mandated by the Companies (Auditor's Report) Order, 2020 for STANDALONE financial statements.

    **INSTRUCTIONS:**
    1. Locate the "Annexure A" section in the standalone audit report
    2. Search for PRIMARY Roman numeral clause headings ONLY: (i), (ii), (iii), (iv), (v), (vi), (vii), (viii), (ix), (x), (xi), (xii), (xiii), (xiv), (xv), (xvi), (xvii), (xviii), (xix), (xx)
    3. Count ONLY the main clause headings - DO NOT count sub-clauses like (a), (b), (A), (B), or alphabetical divisions
    4. Include clauses marked as "not applicable" in your count - they still satisfy the requirement
    5. The document MUST contain exactly 20 Roman numeral clauses to be compliant
    6. Clause (xxi) should NOT be present in standalone CARO

    **COMPLIANCE CRITERIA:**
    - COMPLIANT: Document contains exactly 20 Roman numeral clauses from (i) to (xx) with NO clause (xxi)
    - NON-COMPLIANT: Document contains less than 20, more than 20, missing any clause from (i)-(xx), or contains clause (xxi)

    **ANALYSIS PROCESS:**
    - Extract all Roman numeral patterns in parentheses format: (i), (ii), etc.
    - Create a systematic count of found clauses from (i) to (xx)
    - Identify any missing clauses from the mandatory 20-clause sequence
    - Verify no clause (xxi) is present (as it's only for consolidated statements)
    - Verify no duplicate or extra clause numbers exist

    Return STRICTLY in this format:
    - First line: "Yes" if exactly 20 clauses (i) to (xx) are present with no (xxi), "No" if any deviation
    - Second line: "Total Clauses: X, Missing Clauses: [list missing clauses or 'None']"
    - Third line: "Found Clauses: [list all found Roman numerals from i-xx]"
    - Fourth line: "Compliance Status: [Brief explanation of compliance/non-compliance]"
    - Fifth line: "Recommendation: [Professional high-level explanation and guidance]"

    **EXAMPLE RESPONSES:**

    **If Compliant:**
    Yes
    Total Clauses: 20, Missing Clauses: None
    Found Clauses: (i), (ii), (iii), (iv), (v), (vi), (vii), (viii), (ix), (x), (xi), (xii), (xiii), (xiv), (xv), (xvi), (xvii), (xviii), (xix), (xx)
    Compliance Status: The standalone CARO Annexure demonstrates full compliance with all 20 mandatory clauses properly numbered and sequenced.
    Recommendation: This CARO Annexure for standalone financial statements exemplifies best practices in audit reporting compliance. The presence of all 20 mandatory clauses ensures comprehensive coverage of regulatory requirements under the Companies (Auditor's Report) Order, 2020. This complete compliance strengthens stakeholder confidence, demonstrates thorough audit procedures, and eliminates regulatory risks. The systematic inclusion of all clauses, even those marked as "not applicable," showcases professional diligence and adherence to statutory frameworks, positioning the audit report as a benchmark for regulatory excellence.

    **If Non-Compliant:**
    No
    Total Clauses: 18, Missing Clauses: (xviii), (xix)
    Found Clauses: (i), (ii), (iii), (iv), (v), (vi), (vii), (viii), (ix), (x), (xi), (xii), (xiii), (xiv), (xv), (xvi), (xvii), (xx)
    Compliance Status: Non-compliant due to missing mandatory clauses (xviii) and (xix) from the required 20-clause framework.
    Recommendation: The standalone CARO Annexure requires immediate rectification to include the missing clauses (xviii) and (xix) to achieve full regulatory compliance. Under the Companies (Auditor's Report) Order, 2020, all 20 clauses must be addressed, even if marked as "not applicable." This gap creates significant regulatory exposure and may result in statutory non-compliance issues. The audit firm should revise the annexure to include comprehensive commentary on all missing clauses, ensuring complete adherence to CARO requirements and maintaining professional audit standards.
  `,
  variables: []
},

clause_21: {
  key: 'clause_21',
  template: `
    You are an AI auditor assistant analyzing a CARO (Companies Auditor's Report Order) Annexure document for CONSOLIDATED Financial Statements. Read the given auditor's report annexure under the title "Annexure A" which refers to the 'Report on Other Legal and Regulatory Requirements' for consolidated financial statements.

    **TASK:** Verify that the document contains ONLY clause (xxi) - the single mandatory clause for consolidated financial statements under the Companies (Auditor's Report) Order, 2020.

    **CRITICAL REQUIREMENTS:**
    1. For CONSOLIDATED financial statements, CARO requires ONLY clause (xxi)
    2. Clauses (i) to (xx) should NOT be present in consolidated CARO
    3. The document should contain EXACTLY ONE clause: (xxi)
    4. This is different from standalone financial statements which require clauses (i) to (xx)

    **INSTRUCTIONS:**
    1. Locate the "Annexure A" section in the consolidated audit report
    2. Search specifically for clause (xxi) - the Roman numeral twenty-one
    3. Verify that clauses (i) through (xx) are NOT present (as they are not applicable for consolidated statements)
    4. Confirm the annexure relates to consolidated financial statements, not standalone
    5. The clause (xxi) typically addresses matters related to subsidiary companies and group reporting

    **COMPLIANCE CRITERIA:**
    - COMPLIANT: Document contains ONLY clause (xxi) and NO other Roman numeral clauses (i)-(xx)
    - NON-COMPLIANT: Document contains clauses (i) to (xx), or missing clause (xxi), or contains both (xxi) and other clauses

    **ANALYSIS PROCESS:**
    - Search for the specific Roman numeral pattern: (xxi)
    - Verify no other Roman numeral clauses (i) through (xx) are present
    - Confirm this is a consolidated financial statements annexure
    - Check that clause (xxi) addresses consolidated/group company matters

    Return STRICTLY in this format:
    - First line: "Yes" if ONLY clause (xxi) is present, "No" if clause (xxi) is missing or other clauses are present
    - Second line: "Clause (xxi) Status: [Present/Missing], Other Clauses (i-xx): [Present/Absent]"
    - Third line: "Document Type: [Consolidated/Standalone/Unclear]"
    - Fourth line: "Compliance Status: [Brief explanation of compliance/non-compliance]"
    - Fifth line: "Recommendation: [Professional high-level explanation and guidance]"

    **EXAMPLE RESPONSES:**

    **If Compliant:**
    Yes
    Clause (xxi) Status: Present, Other Clauses (i-xx): Absent
    Document Type: Consolidated Financial Statements CARO
    Compliance Status: Full compliance with consolidated CARO requirements - only clause (xxi) present as mandated.
    Recommendation: This CARO Annexure for consolidated financial statements demonstrates exemplary compliance with regulatory requirements. The presence of only clause (xxi) is precisely what is mandated under the Companies (Auditor's Report) Order, 2020 for consolidated financial statements. This focused approach ensures appropriate reporting on group company matters while avoiding unnecessary duplication of standalone requirements. The auditor has correctly applied the differential CARO framework, showcasing professional understanding of consolidated reporting standards and regulatory nuances, thereby strengthening the credibility of the group audit opinion.

    **If Non-Compliant - Missing (xxi):**
    No
    Clause (xxi) Status: Missing, Other Clauses (i-xx): Absent
    Document Type: Consolidated Financial Statements CARO
    Compliance Status: Non-compliant due to missing mandatory clause (xxi) for consolidated financial statements.
    Recommendation: The CARO Annexure for consolidated financial statements is critically deficient as it lacks the mandatory clause (xxi). This clause is specifically required under the Companies (Auditor's Report) Order, 2020 for consolidated financial statements and addresses essential group company reporting obligations. The absence creates a significant regulatory gap and fails to meet statutory requirements. The audit firm must immediately include clause (xxi) with appropriate commentary on subsidiary companies, joint ventures, and associate companies to ensure full compliance with consolidated reporting standards.

    **If Non-Compliant - Wrong Clauses Present:**
    No
    Clause (xxi) Status: Missing, Other Clauses (i-xx): Present
    Document Type: Appears to be Standalone CARO incorrectly applied to Consolidated
    Compliance Status: Non-compliant - standalone CARO clauses (i) to (xx) incorrectly applied to consolidated financial statements.
    Recommendation: This represents a fundamental misapplication of CARO requirements. The presence of clauses (i) to (xx) indicates a standalone CARO framework has been incorrectly applied to consolidated financial statements. For consolidated statements, only clause (xxi) is required, not the comprehensive standalone clauses. This error demonstrates a critical misunderstanding of regulatory requirements and must be rectified immediately. The audit firm should replace the entire annexure with the appropriate consolidated CARO containing only clause (xxi), ensuring proper compliance with group reporting standards under the Companies (Auditor's Report) Order, 2020.
  `,
  variables: []
},

    // Stage 1.14
benami_property_clause: {
    key: 'benami_property_clause',
  template: `
    You are an AI auditor assistant analyzing a CARO (Companies Auditor's Report Order) Annexure document. 
    
    **TASK:** Verify that clause (i)(e) specifically mentions Benami property proceedings under the Prohibition of Benami Property Transactions Act, 1988.

    **CRITICAL REQUIREMENTS:**
    1. Locate EXACTLY clause "(i)(e)" in the CARO Annexure A
    2. The clause must specifically mention "Benami property" or "benami property"
    3. The clause must reference the "Prohibition of Benami Property Transactions Act, 1988" or "Benami Transactions (Prohibitions) Act, 1988"
    4. The clause must address whether proceedings are initiated/pending or not

    **COMPLIANCE CRITERIA:**
    - COMPLIANT: Clause (i)(e) exists and mentions Benami property with reference to the Act
    - NON-COMPLIANT: Clause (i)(e) missing, or doesn't mention Benami property, or missing Act reference

    **WHAT MAKES IT COMPLIANT:**
    ✅ "No proceedings have been initiated or are pending against the Company for holding any benami property under the Benami Transactions (Prohibitions) Act, 1988"
    ✅ "Proceedings are pending against the Company for holding benami property under the Prohibition of Benami Property Transactions Act, 1988"
    ✅ Any statement in clause (i)(e) that addresses benami property proceedings under the Act

    **WHAT MAKES IT NON-COMPLIANT:**
    ❌ Clause (i)(e) doesn't exist
    ❌ Clause (i)(e) exists but doesn't mention "benami property"
    ❌ Clause (i)(e) mentions benami but doesn't reference the Act
    ❌ Benami property mentioned in wrong clause (not (i)(e))

    **ANALYSIS PROCESS:**
    1. Search for clause "(i)(e)" specifically
    2. Extract the complete text of clause (i)(e)
    3. Check if it contains "benami property" (case-insensitive)
    4. Check if it references the Benami Transactions Act, 1988
    5. Verify it addresses proceedings status (initiated/pending or absence thereof)

    Return STRICTLY in this format:
    - First line: "Yes" if clause (i)(e) properly addresses benami property, "No" if it doesn't
    - Second line: "Clause (i)(e) Status: [Found/Missing], Benami Reference: [Present/Absent]"
    - Third line: "Exact Text: [Complete text of clause (i)(e) if found]"
    - Fourth line: "Compliance Status: [Brief explanation of compliance/non-compliance]"
    - Fifth line: "Recommendation: [Professional guidance for compliance]"

    **EXAMPLE RESPONSES:**

    **If Compliant:**
    Yes
    Clause (i)(e) Status: Found, Benami Reference: Present
    Exact Text: According to the information and explanations given to us and on the basis of our examination of the records of the Company, no proceedings have been initiated or are pending against the Company for holding any benami property under the Benami Transactions (Prohibitions) Act, 1988 (45 of 1988) and rules made thereunder.
    Compliance Status: Full compliance - clause (i)(e) properly addresses benami property proceedings under the required Act.
    Recommendation: This CARO clause (i)(e) demonstrates exemplary compliance with regulatory requirements by specifically addressing benami property proceedings under the Prohibition of Benami Property Transactions Act, 1988. The clause provides clear disclosure regarding the company's status with respect to benami property holdings, ensuring transparency and regulatory adherence. This compliance protects stakeholders and demonstrates the auditor's thorough examination of potential benami property issues, which is crucial for maintaining corporate governance standards and legal compliance.

    **If Non-Compliant - Missing Clause:**
    No
    Clause (i)(e) Status: Missing, Benami Reference: Absent
    Exact Text: Clause (i)(e) not found in the document
    Compliance Status: Non-compliant due to missing mandatory clause (i)(e) addressing benami property.
    Recommendation: The CARO Annexure is critically deficient as it lacks the mandatory clause (i)(e) which must address benami property proceedings under the Prohibition of Benami Property Transactions Act, 1988. This clause is specifically required under the Companies (Auditor's Report) Order, 2020 to ensure transparency regarding potential benami property holdings. The audit firm must immediately include clause (i)(e) with appropriate commentary on benami property proceedings status, ensuring full compliance with CARO requirements and maintaining audit quality standards.

    **If Non-Compliant - Missing Benami Reference:**
    No
    Clause (i)(e) Status: Found, Benami Reference: Absent
    Exact Text: [Whatever text is found in clause (i)(e)]
    Compliance Status: Non-compliant - clause (i)(e) exists but fails to address benami property proceedings.
    Recommendation: While clause (i)(e) is present in the CARO Annexure, it fails to address the mandatory requirement regarding benami property proceedings under the Prohibition of Benami Property Transactions Act, 1988. This represents a significant compliance gap that must be rectified immediately. The clause should specifically state whether any proceedings have been initiated or are pending against the company for holding benami property under the Act. The audit firm must revise clause (i)(e) to include proper benami property disclosure, ensuring complete regulatory compliance and stakeholder transparency.
  `,
  variables: []
},

caro_clause_xiii_related_party: {
  key: 'caro_clause_xiii_related_party',
  template: `
    Analyze this CARO Annexure PDF to check if clause (xiii) properly addresses related party transactions compliance.

    **TASK:** Verify that clause (xiii) contains the required statement about related party transactions compliance with Sections 177 and 188 of the Companies Act.

    **CRITICAL REQUIREMENTS:**
    1. Locate EXACTLY clause "(xiii)" in the CARO Annexure A
    2. The clause must mention compliance with "Section 177 and 188 of the Act"
    3. The clause must reference "related party transactions"
    4. The clause must state that details have been "disclosed" in financial statements as per Indian Accounting Standards

    **KEY COMPLIANCE DETERMINATION:**
    - COMPLIANT: If the clause states that details "have been disclosed" or similar positive disclosure language
    - NON-COMPLIANT: If the clause states "NOT disclosed" or "have NOT been disclosed" or similar negative disclosure language

    **EXPECTED COMPLIANT TEXT (or similar):**
    "In our opinion and according to the information and explanations given to us, the transactions with related parties are in compliance with Section 177 and 188 of the Act, where applicable, and the details of the related party transactions have been disclosed in the standalone financial statements as required by the Indian Accounting Standards"

    **COMPLIANCE CRITERIA:**
    - COMPLIANT: Clause (xiii) exists, mentions Section 177 & 188, AND states that details "have been disclosed" (positive disclosure)
    - NON-COMPLIANT: Clause (xiii) missing, OR doesn't mention Section 177 & 188, OR states "NOT disclosed"/"have NOT been disclosed" (negative disclosure)

    **ANALYSIS PROCESS:**
    1. Search for clause "(xiii)" specifically
    2. Extract the complete text of clause (xiii)
    3. Check if it mentions "Section 177 and 188" or "Sections 177 and 188"
    4. Verify it addresses related party transactions compliance
    5. **CRITICAL CHECK:** Confirm it states that details "have been disclosed" (not "NOT disclosed") regarding financial statement disclosure

    Return STRICTLY in this format:
    - First line: "Yes" if clause (xiii) states "have been disclosed", "No" if it states "NOT disclosed" or similar negative language
    - Second line: "Clause (xiii) Status: [Found/Missing], Section 177&188 Reference: [Present/Absent]"
    - Third line: "Disclosure Check: [Positive disclosure/Negative disclosure/Disclosure word absent]"
    - Fourth line: "Related Party Statement: [Brief summary of what clause states]"
    - Fifth line: "Compliance Status: [Compliant/Non-Compliant with explanation]"
    - Sixth line: "Recommendation: [Professional guidance for compliance]"

    **EXAMPLE RESPONSES:**

    **If Compliant (states "have been disclosed"):**
    Yes
    Clause (xiii) Status: Found, Section 177&188 Reference: Present
    Disclosure Check: Positive disclosure - states "have been disclosed"
    Related Party Statement: Confirms compliance with Section 177 and 188, proper disclosure in financial statements per IAS
    Compliance Status: Compliant - clause properly addresses related party transaction compliance and confirms positive disclosure
    Recommendation: This CARO clause (xiii) demonstrates exemplary compliance with related party transaction regulations. The clause appropriately confirms compliance with Sections 177 and 188 of the Companies Act and states that details "have been disclosed" in financial statements as per Indian Accounting Standards. This compliance strengthens corporate governance framework and demonstrates proper oversight of related party dealings.

    **If Non-Compliant (states "NOT disclosed"):**
    No
    Clause (xiii) Status: Found, Section 177&188 Reference: Present
    Disclosure Check: Negative disclosure - states "NOT disclosed" or "have NOT been disclosed"
    Related Party Statement: May mention Section 177 & 188 but indicates non-disclosure or non-compliance
    Compliance Status: Non-Compliant - clause indicates that details have NOT been disclosed or transactions are NOT in compliance
    Recommendation: The company must immediately address the non-compliance and non-disclosure issues identified in CARO clause (xiii). All related party transactions must comply with Sections 177 and 188 of the Companies Act, and their details must be properly disclosed in financial statements as required by Indian Accounting Standards. The clause currently indicates a finding of non-compliance, which requires corrective action before the clause can reflect positive compliance and disclosure.
  `,
  variables: []
},


// Stage 1.17 - Cost Auditor CARO Clause (vi)
caro_clause_vi_cost_auditor: {
  key: 'caro_clause_vi_cost_auditor',
  template: `
    Analyze this CARO Annexure PDF to check if clause (vi) properly addresses cost records maintenance requirements.

    **CONTEXT:** The company has indicated that they have appointed a Cost Auditor. Therefore, clause (vi) should address cost records maintenance under Section 148 of the Companies Act.

    **TASK:** Verify that clause (vi) contains the required statement about cost records maintenance and examination.

    **CRITICAL REQUIREMENTS:**
    1. Locate EXACTLY clause "(vi)" in the CARO Annexure A
    2. The clause must reference "Section 148 of the Act" or "Sub-Section (1) of Section 148"
    3. The clause must mention "cost records" maintenance requirements
    4. The clause should include the standard statement about cost records examination

    **EXPECTED COMPLIANT TEXT (or similar):**
    "The Central Government has specified maintenance of cost records under Sub-Section (1) of Section 148 of the Act in respect of the products of the Company. We have broadly reviewed the books of account maintained by the Company as specified under sub section (1) of section 148 of the Act, for maintenance of cost records in respect of the products manufactured by the Company, and are of the opinion that prima facie, the prescribed accounts and records have been made and maintained. However, we have not, made a detailed examination of cost records with a view to determine whether they are accurate or complete."

    **COMPLIANCE CRITERIA:**
    - COMPLIANT: Clause (vi) exists and contains required statement about Section 148 cost records maintenance and review
    - NON-COMPLIANT: Clause (vi) missing, or doesn't mention Section 148, or incomplete cost records statement

    **ANALYSIS PROCESS:**
    1. Search for clause "(vi)" specifically
    2. Extract the complete text of clause (vi)
    3. Check if it mentions "Section 148" and "cost records"
    4. Verify it addresses Central Government specification of cost records maintenance
    5. Confirm it includes auditor's opinion on cost records maintenance

    Return STRICTLY in this format:
    - First line: "Yes" if clause (vi) properly addresses cost records, "No" if it doesn't
    - Second line: "Clause (vi) Status: [Found/Missing], Section 148 Reference: [Present/Absent]"
    - Third line: "Cost Records Statement: [Brief summary of what clause states]"
    - Fourth line: "Compliance Status: [Compliant/Non-Compliant with explanation]"
    - Fifth line: "Recommendation: [Professional guidance for compliance]"

    **EXAMPLE RESPONSES:**

    **If Compliant:**
    Yes
    Clause (vi) Status: Found, Section 148 Reference: Present
    Cost Records Statement: Confirms Central Government specification under Section 148, cost records maintained and reviewed
    Compliance Status: Compliant - clause properly addresses cost records maintenance requirements and auditor review
    Recommendation: This CARO clause (vi) demonstrates excellent compliance with cost audit regulations. The clause appropriately confirms that the Central Government has specified cost records maintenance under Section 148 of the Companies Act for the company's products. The auditor's statement that cost records have been broadly reviewed and are prima facie maintained as prescribed shows proper regulatory adherence. The clarification about not conducting detailed examination is appropriate and maintains professional boundaries while ensuring cost audit compliance transparency.

    **If Non-Compliant:**
    No
    Clause (vi) Status: Missing, Section 148 Reference: Absent
    Cost Records Statement: Clause (vi) not found in CARO
    Compliance Status: Non-Compliant - mandatory clause (vi) missing despite Cost Auditor appointment
    Recommendation: The absence of CARO clause (vi) represents a critical compliance gap for a company with appointed Cost Auditor. This clause is mandatory under the Companies (Auditor's Report) Order, 2020 when the Central Government has specified cost records maintenance under Section 148. The audit firm must immediately include clause (vi) with proper statement confirming cost records specification, maintenance review, and auditor's opinion on prescribed accounts and records. This ensures complete regulatory compliance and proper cost audit framework disclosure.
  `,
  variables: []
},
  

  // Interlinked Checks
  caro_interlink: {
    key: 'caro_interlink',
    template: `
      You are comparing Audit Report and CARO Annexure documents.
      
      For Audit Report:
      1) Locate the subheading "Report on Other Legal and Regulatory Requirements"
      2) Find the paragraph number that references "Companies (Auditors' Report) Order, 2020" or "CARO"
      
      For CARO Annexure:
      1) Look for the heading "Annexure A"
      2) Find the line that says "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'"
      
      Return:
      - First line: "Yes" if paragraph numbers match, "No" if they don't match
      - Second line: "Audit Report paragraph: X, CARO reference: Y"
      - Explanation of matching status
      - Recommendation : if this point is compliant needs to give the recommendation with Hilevel Explaination in a Professional way 

    `,
    variables: []
  },

  ifc_interlink: {
    key: 'ifc_interlink',
    template: `
      You are comparing Audit Report and IFC Annexure documents.
      
      For Audit Report:
      1) Under "Report on Other Legal and Regulatory Requirements"
      2) Find the paragraph that references "adequacy of the internal financial controls" and "Annexure B"
      
      For IFC Annexure:
      1) Look for the heading "Annexure B"
      2) Find the line that says "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'"
      
      Return:
      - First line: "Yes" if paragraph numbers match, "No" if they don't match
      - Second line: "Audit Report paragraph: X, IFC reference: Y"
      - Explanation of matching status
      - Recommendation : if this point is compliant needs to give the recommendation with Hilevel Explaination in a Professional way 
    `,
    variables: []
  },

  //stage1.15

  internal_auditor_clause_xiv: {
    key: 'internal_auditor_clause_xiv',
    template: `
      Analyze this CARO Annexure PDF to check if clause (xiv) specifically addresses internal auditor appointment.

      **BACKGROUND ON CARO CLAUSE (xiv):**
      CARO clause (xiv) addresses whether the company has appointed an internal auditor during the year.

    **CONTEXT:**
    The company has indicated that they HAVE appointed an internal auditor. Therefore, clause (xiv) should properly reflect this appointment.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(xiv)" in the CARO document
    2. This clause should address:
       - Whether company has appointed internal auditor during the year
       - Confirmation that internal auditor appointment is in compliance with requirements
       - Any relevant details about the internal auditor appointment

    3. Since the company indicated they HAVE an internal auditor appointed, check if:
       - Clause (xiv) confirms that internal auditor has been appointed
       - Clause (xiv) states compliance with internal auditor requirements
       - Clause (xiv) provides appropriate disclosure about the appointment

    **EVALUATION CRITERIA:**
    - If clause (xiv) is present and confirms internal auditor appointment → Answer "Yes"
    - If clause (xiv) is missing → Answer "No"
    - If clause (xiv) incorrectly states no internal auditor when company has one → Answer "No"
    - If clause (xiv) is present but inadequate/incomplete → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (xiv) properly confirms internal auditor appointment, "No" if missing or inadequate
    - Second line: "Clause (xiv) Status: [Present and Correct/Present but Incorrect/Missing]"
    - Third line: "Internal Auditor Statement: [What the clause states about internal auditor appointment]"
    - Fourth line: "Compliance Status: [Compliant/Non-Compliant/Not Specified]"
    - Fifth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Clause (xiv) Correct (Has Internal Auditor):**
    Yes
    Clause (xiv) Status: Present and Correct
    Internal Auditor Statement: Company has appointed an internal auditor during the year in compliance with applicable requirements
    Compliance Status: Compliant
    Recommendation: CARO clause (xiv) demonstrates proper compliance reporting for internal auditor appointment. The clause correctly confirms that the company has appointed an internal auditor as required, ensuring transparency in governance structure and regulatory adherence. This proper disclosure supports stakeholder confidence in the company's internal control framework.

    **If Clause (xiv) Missing:**
    No
    Clause (xiv) Status: Missing
    Internal Auditor Statement: Clause not found in CARO
    Compliance Status: Not Specified
    Recommendation: The absence of CARO clause (xiv) represents a critical compliance gap. This clause is mandatory under the Companies (Auditor's Report) Order, 2020 to address internal auditor appointment status. Since the company has appointed an internal auditor, this fact must be properly disclosed in clause (xiv). The audit firm must include this clause with appropriate confirmation of the internal auditor appointment.

    **If Clause (xiv) Incorrect (Says No Internal Auditor):**
    No
    Clause (xiv) Status: Present but Incorrect
    Internal Auditor Statement: Clause states no internal auditor appointed or not required
    Compliance Status: Non-Compliant
    Recommendation: CARO clause (xiv) contains inaccurate information. The clause states that no internal auditor is appointed or required, which contradicts the company's actual status of having an internal auditor. This discrepancy must be corrected to accurately reflect the internal auditor appointment, ensuring proper stakeholder disclosure and regulatory compliance.
  `,
  variables: []
},


// Stage 1.18 - CARO Clause (vii)(a) - Regular Statutory Dues
caro_clause_vii_a_statutory_dues: {
  key: 'caro_clause_vii_a_statutory_dues',
  template: `
    Analyze this CARO Annexure PDF to check if clause (vii)(a) properly addresses regular deposit of undisputed statutory dues.

    **TASK:** Verify that clause (vii)(a) contains the required statement about regular deposit of undisputed statutory dues.

    **CRITICAL REQUIREMENTS:**
    1. Locate EXACTLY clause "(vii)(a)" in the CARO Annexure A
    2. The clause must confirm regular deposit of undisputed statutory dues
    3. The clause must specifically mention key statutory dues including:
       - Goods and Service Tax (GST)
       - Provident Fund
       - Employees' State Insurance (ESI)
       - Income-tax
       - Duty of Customs
       - Duty of Excise
       - Cess
       - Any other material statutory dues

    **EXPECTED COMPLIANT TEXT (or similar):**
    "According to the information and explanations given to us and the records of the Company examined by us, the Company has been regular in depositing undisputed statutory dues including Goods and Service Tax, Provident Fund, Employees' State Insurance, Income-tax, Duty of Customs, Duty of Excise, Cess and any other material statutory dues as applicable with the appropriate authorities"

    **COMPLIANCE CRITERIA:**
    - COMPLIANT: Clause (vii)(a) exists and confirms regular deposit of undisputed statutory dues with comprehensive list
    - NON-COMPLIANT: Clause (vii)(a) missing, or doesn't confirm regularity, or missing key statutory dues categories

    **ANALYSIS PROCESS:**
    1. Search for clause "(vii)(a)" specifically
    2. Extract the complete text of clause (vii)(a)
    3. Check if it confirms "regular" deposit of "undisputed" statutory dues
    4. Verify it mentions the key statutory dues categories (GST, PF, ESI, Income Tax, Customs, Excise, Cess)
    5. Confirm it addresses deposits "with appropriate authorities"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (vii)(a) properly addresses regular statutory dues deposit, "No" if it doesn't
    - Second line: "Clause (vii)(a) Status: [Found/Missing], Regularity Confirmed: [Yes/No]"
    - Third line: "Statutory Dues Coverage: [Comprehensive/Partial/Missing]"
    - Fourth line: "Key Dues Mentioned: [List key statutory dues found or 'None']"
    - Fifth line: "Compliance Status: [Compliant/Non-Compliant with explanation]"
    - Sixth line: "Recommendation: [Professional guidance for compliance]"

    **EXAMPLE RESPONSES:**

    **If Compliant:**
    Yes
    Clause (vii)(a) Status: Found, Regularity Confirmed: Yes
    Statutory Dues Coverage: Comprehensive
    Key Dues Mentioned: GST, Provident Fund, ESI, Income-tax, Customs Duty, Excise Duty, Cess
    Compliance Status: Compliant - clause properly confirms regular deposit of undisputed statutory dues with comprehensive coverage
    Recommendation: This CARO clause (vii)(a) demonstrates excellent compliance with statutory dues reporting requirements. The clause appropriately confirms that the company has been regular in depositing undisputed statutory dues, covering all major categories including GST, Provident Fund, ESI, Income Tax, Customs Duty, Excise Duty, and Cess. This comprehensive disclosure ensures stakeholder confidence in the company's regulatory compliance and demonstrates proper tax and statutory obligation management. The confirmation of regularity in deposits indicates strong financial discipline and governance practices.

    **If Non-Compliant:**
    No
    Clause (vii)(a) Status: Found, Regularity Confirmed: No
    Statutory Dues Coverage: Partial
    Key Dues Mentioned: Income-tax, GST (missing PF, ESI, Customs, Excise, Cess)
    Compliance Status: Non-Compliant - clause lacks confirmation of regularity and missing key statutory dues categories
    Recommendation: CARO clause (vii)(a) requires immediate enhancement to meet full compliance requirements. The clause must explicitly confirm that the company has been "regular" in depositing "undisputed" statutory dues. Additionally, the clause must comprehensively address all material statutory dues including Goods and Service Tax, Provident Fund, Employees' State Insurance, Income Tax, Customs Duty, Excise Duty, Cess, and other applicable statutory obligations. This comprehensive disclosure is essential for demonstrating proper regulatory compliance and maintaining stakeholder trust in the company's statutory obligation management.
  `,
  variables: []
},

// Stage 1.19 - CARO Clause (vii)(b) - Disputed Statutory Dues
caro_clause_vii_b_disputed_dues: {
  key: 'caro_clause_vii_b_disputed_dues',
  template: `
    Analyze this CARO Annexure PDF to check if clause (vii)(b) properly addresses disputed statutory dues.

    **TASK:** Verify that clause (vii)(b) contains the required disclosure about disputed statutory dues.

    **CRITICAL REQUIREMENTS:**
    1. Locate EXACTLY clause "(vii)(b)" in the CARO Annexure A
    2. The clause must address disputed statutory dues status
    3. If disputes exist, must provide tabular disclosure with details
    4. If no disputes exist, must clearly state no disputed statutory dues
    5. Must cover key statutory dues: GST, Provident Fund, ESI, Income Tax (including TDS), Sales Tax, Service Tax, Customs Duty, Excise Duty, VAT, Cess

    **COMPLIANCE SCENARIOS:**

    **SCENARIO 1 - NO DISPUTES (Compliant):**
    "According to the information and explanations given to us, there are no disputed amounts relating to statutory dues outstanding as at [date]"

    **SCENARIO 2 - DISPUTES EXIST (Compliant if properly disclosed):**
    Must include tabular disclosure with:
    - Nature of disputed dues
    - Amount involved
    - Forum where dispute is pending
    - Period to which amount relates

    **SCENARIO 3 - NON-COMPLIANT:**
    - Clause missing
    - Clause exists but inadequate disclosure
    - Disputes exist but no proper tabular disclosure

    **ANALYSIS PROCESS:**
    1. Search for clause "(vii)(b)" specifically
    2. Extract the complete text of clause (vii)(b)
    3. Determine if clause states "no disputed statutory dues" OR provides tabular disclosure
    4. If tabular disclosure exists, verify it includes required details
    5. Check coverage of key statutory dues categories

    Return STRICTLY in this format:
    - First line: "Yes" if clause (vii)(b) properly addresses disputed dues, "No" if it doesn't
    - Second line: "Clause (vii)(b) Status: [Found/Missing]"
    - Third line: "Disputed Dues Status: [No Disputes/Disputes Disclosed/Disputes Not Properly Disclosed]"
    - Fourth line: "Disclosure Type: [Clear No Disputes Statement/Tabular Disclosure/Inadequate/Missing]"
    - Fifth line: "Compliance Status: [Compliant/Non-Compliant with explanation]"
    - Sixth line: "Recommendation: [Professional guidance for compliance]"

    **EXAMPLE RESPONSES:**

    **If Compliant (No Disputes):**
    Yes
    Clause (vii)(b) Status: Found
    Disputed Dues Status: No Disputes
    Disclosure Type: Clear No Disputes Statement
    Compliance Status: Compliant - clause clearly states no disputed statutory dues outstanding
    Recommendation: This CARO clause (vii)(b) demonstrates excellent compliance by providing clear confirmation that no disputed statutory dues are outstanding. This transparent disclosure strengthens stakeholder confidence and indicates effective tax and regulatory compliance management. The absence of disputed statutory dues reflects strong legal and financial management practices, reducing regulatory risks and ensuring smooth business operations.

    **If Compliant (Disputes Properly Disclosed):**
    Yes
    Clause (vii)(b) Status: Found
    Disputed Dues Status: Disputes Disclosed
    Disclosure Type: Tabular Disclosure
    Compliance Status: Compliant - clause provides proper tabular disclosure of disputed statutory dues with required details
    Recommendation: This CARO clause (vii)(b) demonstrates proper compliance by providing comprehensive tabular disclosure of disputed statutory dues including nature, amounts, forums, and periods. This transparent approach ensures stakeholder awareness of potential contingent liabilities and demonstrates proactive legal and financial management. The detailed disclosure enables informed decision-making and maintains regulatory compliance standards.

    **If Non-Compliant:**
    No
    Clause (vii)(b) Status: Missing
    Disputed Dues Status: Not Addressed
    Disclosure Type: Missing
    Compliance Status: Non-Compliant - mandatory clause (vii)(b) missing from CARO
    Recommendation: The absence of CARO clause (vii)(b) represents a critical compliance gap that must be addressed immediately. This clause is mandatory under the Companies (Auditor's Report) Order, 2020 to disclose the status of disputed statutory dues. The clause must either clearly state that no disputed statutory dues are outstanding or provide detailed tabular disclosure of any disputes including nature, amounts, forums, and periods. This disclosure is essential for stakeholder transparency and regulatory compliance.
  `,
  variables: []
},

profit_loss_opinion: {
  key: 'profit_loss_opinion',
  template: `
    Analyze this audit report PDF to check if the profit/loss mentioned in the auditor's opinion matches the expected financial result.

    **TASK:** Verify that the financial result mentioned in the "Opinion" section matches the user's selection of "{{profit_or_loss}}".

    **SPECIFIC INSTRUCTIONS:**
    1. First, locate the "INDEPENDENT AUDITORS' REPORT" section.
    
    2. Within this section, find the paragraph under the "Opinion" heading.
       - This paragraph typically starts right after "Opinion" and ends before the next heading.
    
    3. In the opinion paragraph, look for mentions of financial performance such as:
       - "profit for the year"
       - "loss for the year" 
       - "net profit"
       - "net loss"
       - "surplus for the year"
       - "deficit for the year"
       - Any similar financial result indicators
    
    4. Extract the exact phrase that indicates whether the company made a profit or loss.
    
    5. Compare this with the expected result: "{{profit_or_loss}}"
    
    **EVALUATION CRITERIA:**
    - COMPLIANT: If user selected "Profit" and opinion mentions profit/surplus
    - COMPLIANT: If user selected "Loss" and opinion mentions loss/deficit
    - NON-COMPLIANT: If user selected "Profit" but opinion mentions loss/deficit
    - NON-COMPLIANT: If user selected "Loss" but opinion mentions profit/surplus
    - NON-COMPLIANT: If financial result cannot be clearly determined from opinion

    **EXPECTED USER SELECTION:** {{profit_or_loss}}

    Return STRICTLY in this format:
    - First line: "Yes" if the financial result matches user selection, "No" if it doesn't match
    - Second line: "User Expected: {{profit_or_loss}}, Found in Opinion: [Profit/Loss/Unclear]"
    - Third line: "Exact Text: [Quote the relevant text from opinion paragraph]"
    - Fourth line: "Compliance Status: [Compliant/Non-Compliant with brief explanation]"
    - Fifth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Compliant (User selected Profit, Opinion shows Profit):**
    Yes
    User Expected: Profit, Found in Opinion: Profit
    Exact Text: "the Company has earned a net profit of ₹15,42,000 for the year ended March 31, 2024"
    Compliance Status: Compliant - Opinion correctly reflects profit as expected by user selection
    Recommendation: The auditor's opinion appropriately reflects the company's profitable performance for the year. This consistency between expected financial results and audit opinion demonstrates accurate financial reporting and supports stakeholder confidence in the company's financial statements.

    **If Non-Compliant (User selected Profit, Opinion shows Loss):**
    No
    User Expected: Profit, Found in Opinion: Loss
    Exact Text: "the Company has incurred a net loss of ₹8,75,000 for the year ended March 31, 2024"
    Compliance Status: Non-Compliant - Opinion indicates loss while user expected profit
    Recommendation: There is a significant discrepancy between the expected financial result (Profit) and the actual result disclosed in the auditor's opinion (Loss). This mismatch requires immediate attention to verify the accuracy of financial data and user expectations.
  `,
  variables: ['profit_or_loss']
},





};

// Combine base prompts with multi-document prompts
export const PROMPT_TEMPLATES: Record<string, PromptTemplate> = addMultiDocumentPrompts(BASE_PROMPT_TEMPLATES);

// Function to render prompt template with variables
export function renderPrompt(promptKey: string, variables: Record<string, any>): string {
  const template = PROMPT_TEMPLATES[promptKey];
  if (!template) {
    throw new Error(`Prompt template not found: ${promptKey}. Available prompts: ${Object.keys(PROMPT_TEMPLATES).join(', ')}`);
  }
  
  let rendered = template.template;
  
  // Replace variables in format {{variable_name}}
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g');
    rendered = rendered.replace(regex, String(value));
  }
  
  // Handle conditional blocks {{#if_condition}} ... {{else}} ... {{/if_condition}}
  // Simple implementation for top_1000_or_500 condition
  if (variables.top_1000_or_500 === 'Yes') {
    rendered = rendered.replace(/{{#if_top_1000_500}}([\s\S]*?){{else}}([\s\S]*?){{\/if_top_1000_500}}/g, '$1');
  } else {
    rendered = rendered.replace(/{{#if_top_1000_500}}([\s\S]*?){{else}}([\s\S]*?){{\/if_top_1000_500}}/g, '$2');
  }
  
  return rendered.trim();
}

// Export helper functions
export { addMultiDocumentPrompts } from './multiDocumentPrompts';