import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle, Wifi, Shield } from 'lucide-react';

interface AuthErrorHandlerProps {
  error: string;
  onRetry: () => void;
  onRefresh: () => void;
  loading?: boolean;
}

const AuthErrorHandler: React.FC<AuthErrorHandlerProps> = ({ 
  error, 
  onRetry, 
  onRefresh, 
  loading = false 
}) => {
  const getErrorIcon = () => {
    if (error.includes('network') || error.includes('connection')) {
      return <Wifi className="h-5 w-5" />;
    }
    if (error.includes('internal') || error.includes('configuration')) {
      return <Shield className="h-5 w-5" />;
    }
    return <AlertTriangle className="h-5 w-5" />;
  };

  const getErrorSuggestion = () => {
    if (error.includes('internal')) {
      return {
        title: 'Service Temporarily Unavailable',
        description: 'The authentication service is experiencing issues. This usually resolves quickly.',
        actions: [
          { label: 'Refresh Page', action: onRefresh, primary: true },
          { label: 'Try Again', action: onRetry, primary: false }
        ]
      };
    }
    
    if (error.includes('network') || error.includes('connection')) {
      return {
        title: 'Connection Issue',
        description: 'Please check your internet connection and try again.',
        actions: [
          { label: 'Try Again', action: onRetry, primary: true }
        ]
      };
    }
    
    if (error.includes('popup')) {
      return {
        title: 'Popup Issue',
        description: 'Please ensure popups are allowed for this site and try again.',
        actions: [
          { label: 'Try Again', action: onRetry, primary: true }
        ]
      };
    }
    
    if (error.includes('configuration') || error.includes('tenant')) {
      return {
        title: 'Configuration Issue',
        description: 'There\'s an issue with the authentication setup. Please contact support.',
        actions: [
          { label: 'Refresh Page', action: onRefresh, primary: true }
        ]
      };
    }
    
    return {
      title: 'Authentication Error',
      description: 'An error occurred during sign-in. Please try again.',
      actions: [
        { label: 'Try Again', action: onRetry, primary: true }
      ]
    };
  };

  const errorInfo = getErrorSuggestion();

  return (
    <div className="bg-red-500/10 border border-red-500/20 text-red-200 p-6 rounded-xl backdrop-blur-sm">
      <div className="flex items-start gap-4">
        <div className="h-8 w-8 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
          {getErrorIcon()}
        </div>
        
        <div className="flex-1 space-y-4">
          <div>
            <h3 className="font-semibold text-red-100 mb-2">{errorInfo.title}</h3>
            <p className="text-sm text-red-200/80 mb-2">{errorInfo.description}</p>
            <details className="text-xs text-red-300/60">
              <summary className="cursor-pointer hover:text-red-200/80">Technical details</summary>
              <p className="mt-2 font-mono bg-red-500/10 p-2 rounded border border-red-500/20">
                {error}
              </p>
            </details>
          </div>
          
          <div className="flex gap-3">
            {errorInfo.actions.map((action, index) => (
              <Button
                key={index}
                type="button"
                onClick={action.action}
                disabled={loading}
                variant={action.primary ? "default" : "outline"}
                size="sm"
                className={action.primary 
                  ? "bg-red-600 hover:bg-red-700 text-white" 
                  : "border-red-500/30 text-red-200 hover:bg-red-500/10"
                }
              >
                {loading && action.primary ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                {action.label}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthErrorHandler;
