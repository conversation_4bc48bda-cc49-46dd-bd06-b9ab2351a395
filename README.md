# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/d0f70fa5-4040-4a4f-ab7a-0ee74d4d4540

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/d0f70fa5-4040-4a4f-ab7a-0ee74d4d4540) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/d0f70fa5-4040-4a4f-ab7a-0ee74d4d4540) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes it is!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

## Demo Login

This application includes user authentication via Firebase.

### Setup Environment Variables

1. Copy `.env.example` to `.env`:
   ```sh
   cp .env.example .env
   ```

2. Update the `.env` file with your Firebase configuration:
   ```env
   VITE_FIREBASE_API_KEY=your_firebase_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   # ... other Firebase config
   VITE_GEMINI_API_KEY=your_gemini_api_key
   ```

### 🔑 Gemini API Setup

This application uses Google's Gemini AI for document analysis. **See [GEMINI_API_SETUP.md](./GEMINI_API_SETUP.md) for detailed setup instructions.**

Quick setup:
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create an API key
3. Add it to your `.env` file as `VITE_GEMINI_API_KEY=your_key_here`

### Firebase Setup

To set up Firebase authentication and security:

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication and configure your preferred sign-in methods
3. Enable Realtime Database with the rules from `firebase-database-rules.json`
4. Get your Firebase configuration from Project Settings
5. Update the environment variables in `.env` with your Firebase config

### 🔒 Firebase App Check (Enhanced Security)

This application includes Firebase App Check as an additional security layer to protect against abuse and unauthorized access.

**Setup Instructions:**
1. Follow the detailed guide in [FIREBASE_APP_CHECK_SETUP.md](./FIREBASE_APP_CHECK_SETUP.md)
2. Get a reCAPTCHA v3 site key from [Google reCAPTCHA](https://www.google.com/recaptcha/admin)
3. Add the site key to your `.env` file:
   ```env
   VITE_RECAPTCHA_SITE_KEY=your_recaptcha_v3_site_key_here
   ```

**Benefits:**
- Protects Firebase resources from abuse
- Verifies requests come from your authentic app
- Works seamlessly with existing authentication
- Provides bot protection and enhanced security
