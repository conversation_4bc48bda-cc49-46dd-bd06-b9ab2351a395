// geminiProcessor.ts - Clean Gemini AI integration

import { CheckResult } from './checkDefinitions';
import { renderPrompt } from './promptDefinitions';

// Gemini API configuration
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent";

// Validate API key
if (!API_KEY) {
  console.error('VITE_GEMINI_API_KEY environment variable not set. Please add it to your .env file.');
}

const AUDITOR_RESIGNATION_API_KEY = import.meta.env.VITE_GEMINI_API_KEY; // Using the same API key
const AUDITOR_RESIGNATION_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-05-20:generateContent"; // Different model

// Cache for PDF base64 data to avoid repeated conversions
const pdfCache = new Map<string, string>();

/**
 * Convert ArrayBuffer to Base64 string (browser-compatible)
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

/**
 * Get PDF as Base64 string with caching
 */
async function getPdfAsBase64(pdfFile: File): Promise<string> {
  const cacheKey = `${pdfFile.name}_${pdfFile.size}`;
  
  if (pdfCache.has(cacheKey)) {
    console.log(`Using cached PDF data for ${pdfFile.name}`);
    return pdfCache.get(cacheKey)!;
  }
  
  const pdfArrayBuffer = await pdfFile.arrayBuffer();
  const pdfBase64 = arrayBufferToBase64(pdfArrayBuffer);
  
  pdfCache.set(cacheKey, pdfBase64);
  console.log(`Cached PDF data for ${pdfFile.name}`);
  
  return pdfBase64;
}

/**
 * Call Gemini API with retry logic
 */
async function callGeminiAPI(prompt: string, pdfBase64: string, maxRetries: number = 3): Promise<any> {
  // Check if API key is available
  if (!API_KEY) {
    throw new Error('Gemini API key not found. Please set VITE_GEMINI_API_KEY in your .env file.');
  }

  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`${API_URL}?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [
              { text: prompt },
              {
                inline_data: {
                  mime_type: "application/pdf",
                  data: pdfBase64
                }
              }
            ]
          }],
          generation_config: {
            temperature: 0.1,
            top_k: 32,
            top_p: 0.95,
            max_output_tokens: 8192,
          }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        
        // Validate response structure
        if (!data.candidates || !data.candidates[0] || 
            !data.candidates[0].content || !data.candidates[0].content.parts || 
            !data.candidates[0].content.parts[0].text) {
          throw new Error("Unexpected response format from Gemini API");
        }
        
        return data;
      } else {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      retryCount++;
      console.warn(`Retry ${retryCount}/${maxRetries} due to: ${error}`);

      if (retryCount >= maxRetries) {
        throw error;
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
    }
  }
  
  throw new Error(`Failed to get response from Gemini API after ${maxRetries} attempts`);
}

/**
 * Parse Gemini API response into CheckResult
 */
function parseGeminiResponse(responseText: string, checkType: string): CheckResult {
  const lines = responseText.split('\n');
  const firstLine = lines[0].trim().toLowerCase();
  const explanation = lines.slice(1).join('\n').trim();

  // Determine compliance
  let isCompliant = firstLine === 'yes';
  
  // Special handling for specific check types
  if (checkType === "profit_loss") {
    isCompliant = firstLine === 'compliant';
  }

  // Extract additional data for specific checks
  let extractedData = null;
  
  switch (checkType) {
    case 'signature_date':
      if (lines.length > 1) {
        extractedData = { signatureBlock: lines.slice(1).join('\n') };
      }
      break;
      
    case 'company_name_consistency':
      if (lines.length >= 3) {
        extractedData = {
          totalMentions: lines[1].trim(),
          variations: lines[2].trim(),
          details: lines.slice(3).join('\n').trim()
        };
      }
      break;
      
    case 'caro_interlink':
    case 'ifc_interlink':
      if (lines.length >= 2) {
        extractedData = { paragraphComparison: lines[1].trim() };
      }
      break;
  }

  return {
    isCompliant,
    explanation: explanation || `${checkType} check completed`,
    confidence: 0.9,
    extractedData,
    detail: lines.length > 2 ? lines.slice(2).join('\n').trim() : ""
  };
}

/**
 * Process a single document check using Gemini AI
 */
export async function processSingleDocumentCheck(
  pdfFile: File,
  checkType: string,
  promptKey: string,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log(`Processing ${checkType} check with Gemini AI`);

    // Get PDF as Base64
    const pdfBase64 = await getPdfAsBase64(pdfFile);

    // Render prompt with parameters
    const prompt = renderPrompt(promptKey, parameters);

    // Call Gemini API
    const data = await callGeminiAPI(prompt, pdfBase64);

    // Parse response
    const responseText = data.candidates[0].content.parts[0].text;
    const result = parseGeminiResponse(responseText, checkType);

    console.log(`Successfully processed ${checkType} check:`, result);
    return result;

  } catch (error) {
    console.error(`Error processing ${checkType} check:`, error);

    return {
      isCompliant: false,
      explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      extractedData: null,
      detail: ""
    };
  }
}

/**
 * Clear PDF cache for memory management
 */
export function clearPdfCache(): void {
  pdfCache.clear();
  console.log('PDF cache cleared');
}

/**
 * Get cache statistics
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return {
    size: pdfCache.size,
    keys: Array.from(pdfCache.keys())
  };
}

/**
 * Call Gemini API specifically for auditor resignation checks using different model
 */
async function callAuditorResignationGeminiAPI(prompt: string, pdfBase64: string, maxRetries: number = 3): Promise<any> {
  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 45000); // Longer timeout for pro model

      const response = await fetch(`${AUDITOR_RESIGNATION_API_URL}?key=${AUDITOR_RESIGNATION_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [
              { text: prompt },
              {
                inline_data: {
                  mime_type: "application/pdf",
                  data: pdfBase64
                }
              }
            ]
          }],
          generation_config: {
            temperature: 0.05, // Lower temperature for more consistent results
            top_k: 40,
            top_p: 0.9,
            max_output_tokens: 4096,
          }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        
        // Validate response structure
        if (!data.candidates || !data.candidates[0] || 
            !data.candidates[0].content || !data.candidates[0].content.parts || 
            !data.candidates[0].content.parts[0].text) {
          throw new Error("Unexpected response format from Auditor Resignation Gemini API");
        }
        
        return data;
      } else {
        throw new Error(`Auditor Resignation Gemini API error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      retryCount++;
      console.warn(`Auditor Resignation Retry ${retryCount}/${maxRetries} due to: ${error}`);

      if (retryCount >= maxRetries) {
        throw error;
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1500 * Math.pow(2, retryCount)));
    }
  }
  
  throw new Error(`Failed to get response from Auditor Resignation Gemini API after ${maxRetries} attempts`);
}

/**
 * Process auditor resignation checks using separate Gemini model
 */
export async function processAuditorResignationCheck(
  pdfFile: File,
  checkType: string,
  promptKey: string,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log(`🔍 Processing ${checkType} check with SEPARATE Gemini model for auditor resignation`);

    // Get PDF as Base64
    const pdfBase64 = await getPdfAsBase64(pdfFile);

    // Render prompt with parameters
    const prompt = renderPrompt(promptKey, parameters);

    // Call separate Gemini API for auditor resignation
    const data = await callAuditorResignationGeminiAPI(prompt, pdfBase64);

    // Parse response
    const responseText = data.candidates[0].content.parts[0].text;
    const result = parseAuditorResignationResponse(responseText, checkType);

    console.log(`✅ Successfully processed ${checkType} check with separate model:`, result);
    return result;

  } catch (error) {
    console.error(`❌ Error processing ${checkType} check with separate model:`, error);

    return {
      isCompliant: false,
      explanation: `Error processing auditor resignation check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      extractedData: null,
      detail: ""
    };
  }
}

/**
 * Parse auditor resignation response into CheckResult
 */
function parseAuditorResignationResponse(responseText: string, checkType: string): CheckResult {
  const lines = responseText.split('\n').filter(line => line.trim());
  
  console.log(`📄 Parsing auditor resignation response (${lines.length} lines):`, lines);

  // Default structure
  const result: any = {
    isCompliant: false,
    explanation: responseText,
    confidence: 0.9,
    extractedData: {},
    detail: ""
  };

  // Parse based on check type
  if (checkType.includes('annual_report_auditor_resignation')) {
    // Parse Annual Report response
    const auditorResignationData: any = {};
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('Has_Auditor_Resignation:')) {
        const value = trimmedLine.split(':')[1]?.trim().toLowerCase();
        auditorResignationData.hasAuditorResignation = value === 'yes';
      } else if (trimmedLine.startsWith('Resignation_Details:')) {
        auditorResignationData.resignationDetails = trimmedLine.split(':')[1]?.trim();
      } else if (trimmedLine.startsWith('Filing_References:')) {
        auditorResignationData.filingReferences = trimmedLine.split(':')[1]?.trim();
      } else if (trimmedLine.startsWith('Location_In_Report:')) {
        auditorResignationData.locationInReport = trimmedLine.split(':')[1]?.trim();
      } else if (trimmedLine.startsWith('Resignation_Reason:')) {
        auditorResignationData.resignationReason = trimmedLine.split(':')[1]?.trim();
      }
    });
    
    result.extractedData = { auditorResignationData };
    result.isCompliant = true; // This step always succeeds if parsing works
    
  } else if (checkType.includes('caro_clause_xviii')) {
    // Parse CARO response
    const caroClauseXVIIIData: any = {};
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('Has_Clause_XVIII:')) {
        const value = trimmedLine.split(':')[1]?.trim().toLowerCase();
        caroClauseXVIIIData.hasClauseXVIII = value === 'yes';
      } else if (trimmedLine.startsWith('Clause_XVIII_Status:')) {
        caroClauseXVIIIData.clauseXVIIIStatus = trimmedLine.split(':')[1]?.trim();
      } else if (trimmedLine.startsWith('Auditor_Resignation_Mentioned:')) {
        const value = trimmedLine.split(':')[1]?.trim().toLowerCase();
        caroClauseXVIIIData.auditorResignationMentioned = value === 'yes';
      } else if (trimmedLine.startsWith('Clause_Content_Summary:')) {
        caroClauseXVIIIData.clauseContentSummary = trimmedLine.split(':')[1]?.trim();
      } else if (trimmedLine.startsWith('Compliance_Indication:')) {
        caroClauseXVIIIData.complianceIndication = trimmedLine.split(':')[1]?.trim();
      }
    });
    
    result.extractedData = { caroClauseXVIIIData };
    result.isCompliant = true; // This step always succeeds if parsing works
  }

  console.log(`📊 Parsed auditor resignation data:`, result.extractedData);
  return result;
}