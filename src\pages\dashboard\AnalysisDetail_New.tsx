// AnalysisDetail.tsx - Updated to work with Firebase Realtime Database

import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { getAnalysisById, StoredAnalysisResult } from "@/lib/firebaseStorageService";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Download, FileText, Eye } from "lucide-react";
import AnalysisResults from "@/components/analyzer/AnalysisResults";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import SimpleDocumentViewer from "@/components/common/SimpleDocumentViewer";

const AnalysisDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [analysisData, setAnalysisData] = useState<StoredAnalysisResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedDocumentUrl, setSelectedDocumentUrl] = useState<string | null>(null);
  const [selectedDocumentName, setSelectedDocumentName] = useState<string>("");

  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!id || !currentUser) {
        setError("Invalid analysis ID or not authenticated");
        setLoading(false);
        return;
      }

      try {
        console.log(`Fetching analysis with ID: ${id}`);
        const data = await getAnalysisById(id, currentUser.uid);
        
        if (!data) {
          setError("Analysis not found");
          return;
        }

        console.log("Analysis data loaded:", data);
        setAnalysisData(data);
        
      } catch (error) {
        console.error("Error fetching analysis:", error);
        setError(error instanceof Error ? error.message : "Failed to load analysis");
        toast({
          title: "Error",
          description: "Failed to load analysis data",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();
  }, [id, currentUser, toast]);

  const handleBackClick = () => {
    navigate("/dashboard/history");
  };

  const handleExportPDF = () => {
    toast({
      title: "Feature Coming Soon",
      description: "PDF export will be available in a future update.",
    });
  };

  const handleViewDocument = (documentInfo: any, documentName: string) => {
    // Since we're now storing metadata instead of files, we can't view documents directly
    toast({
      title: "Document Viewing",
      description: "Document viewing is not available for this analysis format.",
      variant: "default",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={handleBackClick} variant="outline">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to History
        </Button>
      </div>
    );
  }

  if (!analysisData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 mb-4">Analysis not found</p>
        <Button onClick={handleBackClick} variant="outline">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to History
        </Button>
      </div>
    );
  }

  // Document type labels for display
  const documentLabels: Record<string, string> = {
    audit_report: "Audit Report",
    annexure_a: "Annexure A",
    annexure_b: "Annexure B", 
    balance_sheet: "Balance Sheet",
    notes: "Notes to Financial Statements",
    pl_notes: "P&L Notes",
    annual_report: "Annual Report",
    secretarial_compliance: "Secretarial Compliance",
    cfs: "Consolidated Financial Statements"
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            onClick={handleBackClick} 
            variant="outline" 
            size="sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {analysisData.companyName}
            </h1>
            <p className="text-gray-500">
              Analysis performed on {format(new Date(analysisData.timestamp), "MMM d, yyyy 'at' h:mm a")}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleExportPDF} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {analysisData.summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {analysisData.summary.total}
            </div>
            <div className="text-sm text-blue-800">Total Checks</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {analysisData.summary.compliant}
            </div>
            <div className="text-sm text-green-800">Compliant</div>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-red-600">
              {analysisData.summary.nonCompliant}
            </div>
            <div className="text-sm text-red-800">Non-Compliant</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {analysisData.summary.compliancePercentage}%
            </div>
            <div className="text-sm text-purple-800">Compliance Rate</div>
          </div>
        </div>
      )}

      {/* Documents Section */}
      {analysisData.documents && Object.keys(analysisData.documents).length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Analyzed Documents
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(analysisData.documents).map(([docType, docInfo]) => {
              if (!docInfo) return null;
              
              const label = documentLabels[docType] || docType;
              
              return (
                <div
                  key={docType}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm truncate">{label}</h3>
                      <p className="text-xs text-gray-500 truncate">
                        {docInfo.name}
                      </p>
                      <p className="text-xs text-gray-400">
                        {(docInfo.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewDocument(docInfo, label)}
                      disabled
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Analysis Results */}
      {analysisData.results && (
        <div className="bg-white rounded-lg shadow-sm">
          <AnalysisResults 
            results={analysisData.results}
            companyName={analysisData.companyName}
            summary={analysisData.summary}
          />
        </div>
      )}

      {/* Document Viewer Modal */}
      {viewerOpen && selectedDocumentUrl && (
        <SimpleDocumentViewer
          isOpen={viewerOpen}
          onClose={() => {
            setViewerOpen(false);
            if (selectedDocumentUrl) {
              URL.revokeObjectURL(selectedDocumentUrl);
            }
            setSelectedDocumentUrl(null);
            setSelectedDocumentName("");
          }}
          documentUrl={selectedDocumentUrl}
          documentName={selectedDocumentName}
        />
      )}
    </div>
  );
};

export default AnalysisDetail;
