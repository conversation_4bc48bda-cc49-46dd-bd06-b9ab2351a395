// AnalysisHistory.tsx - Updated to work with Firebase Realtime Database

import React, { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { getAnalysisHistory, StoredAnalysisResult } from "@/lib/firebaseStorageService";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, CalendarIcon, ChevronRight, ListFilter } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

const AnalysisHistory = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [history, setHistory] = useState<StoredAnalysisResult[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentUser) {
      fetchAnalysisHistory();
    }
  }, [currentUser]);

  const fetchAnalysisHistory = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const analysisHistory = await getAnalysisHistory(currentUser.uid);
      setHistory(analysisHistory);
    } catch (error) {
      console.error("Error fetching analysis history:", error);
      toast({
        title: "Error",
        description: "Failed to load analysis history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewAnalysis = (analysisId: string) => {
    navigate(`/dashboard/analysis/${analysisId}`);
  };

  // Get results summary - now using the stored summary
  const getResultsSummary = (analysis: StoredAnalysisResult) => {
    return analysis.summary || {
      total: 0,
      compliant: 0,
      nonCompliant: 0,
      compliancePercentage: 0
    };
  };

  // Count documents in analysis
  const getDocumentCount = (analysis: StoredAnalysisResult) => {
    if (!analysis.documents) return 0;
    
    return Object.values(analysis.documents).filter(doc => doc !== null && doc !== undefined).length;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Analysis History</h1>
        <Button onClick={() => navigate("/dashboard/analyzer")}>
          New Analysis
        </Button>
      </div>

      <div>
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : history.length === 0 ? (
          <Card>
            <CardContent className="py-12">
              <div className="text-center">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">No analysis history</h3>
                <p className="text-gray-500 mb-6">
                  You haven't performed any analysis yet. Start by analyzing your documents.
                </p>
                <Button onClick={() => navigate("/dashboard/analyzer")}>
                  Start New Analysis
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Your Previous Analyses</CardTitle>
                <Button variant="outline" size="sm">
                  <ListFilter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {history.map((analysis) => {
                  const summary = getResultsSummary(analysis);
                  const documentCount = getDocumentCount(analysis);

                  return (
                    <div
                      key={analysis.id}
                      className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => handleViewAnalysis(analysis.id)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">                          <h3 className="font-medium text-lg">
                            {analysis.companyName || analysis.parameters?.company_name || "Unknown Company"}
                          </h3>
                          <div className="flex items-center text-sm text-gray-500 mt-1">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            <span>
                              Analyzed on {format(new Date(analysis.timestamp), "MMM d, yyyy 'at' h:mm a")}
                            </span>
                          </div>
                        

                          <div className="flex items-center text-sm text-gray-500 mt-1">
                            <FileText className="h-4 w-4 mr-1" />
                            <span>
                              {documentCount} document{documentCount !== 1 ? 's' : ''} analyzed
                            </span>
                          </div>
                          
                          {/* Report type and additional info */}
                          <div className="flex items-center text-sm text-gray-500 mt-1">
                            <span>
                              {analysis.parameters?.audit_report_type || 'Standard'} Report
                              {analysis.parameters?.is_nbfc === 'Yes' && ' • NBFC'}
                              {analysis.parameters?.company_listing_status === 'Listed' && ' • Listed'}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          <ChevronRight className="h-5 w-5 text-gray-400" />
                        </div>
                      </div>

                      {/* Summary badges */}
                      <div className="flex mt-4 space-x-3">
                        <div className="flex items-center bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm">
                          <span className="font-medium mr-1">{summary.total}</span> Total Checks
                        </div>
                        
                        <div className="flex items-center bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm">
                          <span className="font-medium mr-1">{summary.compliant}</span> Passed
                        </div>
                        
                        {summary.nonCompliant > 0 && (
                          <div className="flex items-center bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm">
                            <span className="font-medium mr-1">{summary.nonCompliant}</span> Failed
                          </div>
                        )}
                        
                        <div className="flex items-center bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm">
                          <span className="font-medium mr-1">{summary.compliancePercentage}%</span> Compliance
                        </div>
                      </div>
                      
                      {/* Compliance progress bar */}
                      <div className="mt-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              summary.compliancePercentage < 50 ? "bg-red-500" :
                              summary.compliancePercentage < 80 ? "bg-yellow-500" : "bg-green-500"
                            }`}
                            style={{ width: `${summary.compliancePercentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AnalysisHistory;