// checkDefinitions.ts - Updated with multi-document check support

export interface CheckResult {
  isCompliant: boolean;
  explanation: string;
  confidence?: number;
  extractedData?: any;
  detail?: string;
  evidence?: string;
  source?: string; // Added for categorization
}

export interface CheckDefinition {
  id: string;
  name: string;
  description: string;
  type: 'independent' | 'interlinked' | 'multi-document';
  documentTypes: string[];
  conditions: CheckCondition[];
  promptKey: string;
  category?: string; // Added for better organization
}

export interface CheckCondition {
  parameter: string;
  operator: 'equals' | 'not_equals' | 'includes' | 'greater_than' | 'less_than';
  value: any;
  logic?: 'AND' | 'OR';
}

// Independent Document Checks
export const INDEPENDENT_CHECKS: CheckDefinition[] = [

  {
  id: 'financial_statement_type_check',
  name: 'Financial Statement Type Verification',
  description: 'Ctrl+F search to count instances of Standalone/Consolidated financial statements',
  type: 'independent',
  documentTypes: ['audit_report'],
  conditions: [
    {
      parameter: 'audit_report_type',
      operator: 'includes',
      value: ['Standalone', 'Consolidated']
    }
  ],
  promptKey: 'financial_statement_type_check',
  category: 'Statement Type Compliance'
},

{
    id: 'consolidated_financial_statements',
    name: 'Consolidated Financial Statements Verification',
    description: 'Verifies the consolidated financial statements document',
    documentType: 'cfs',
    prompt: 'Verify that this document contains consolidated financial statements. Look for terms like "consolidated", "group", or references to subsidiary companies. Check if there are consolidated balance sheet, income statement, cash flow statement, and notes to consolidated financial statements. Return COMPLIANT if the document is clearly a consolidated financial statement, otherwise NON-COMPLIANT with explanation.'
  },

{
  id: 'audit_opinion_type_check',
  name: 'Audit Opinion Type Verification',
  description: 'Verifies instances of specific audit opinion type (Adverse/Qualified) when applicable',
  type: 'independent',
  documentTypes: ['audit_report'],
  conditions: [
    {
      parameter: 'audit_opinion_type',
      operator: 'includes',
      value: ['Adverse', 'Qualified']
    }
  ],
  promptKey: 'audit_opinion_type_check',
  category: 'Opinion Type Compliance'
},
  {
    id: 'audit_title',
    name: 'Audit Title',
    description: 'Checking for heading "Independent Auditor\'s Report"',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [], // Always runs
    promptKey: 'audit_title',
    category: 'Basic Compliance'
  },
  {
    id: 'address_to_members',
    name: 'Address to Members',
    description: 'Verifying format "To the Members of [Company Name]"',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [], // Always runs
    promptKey: 'company_format',
    category: 'Basic Compliance'
  },
  {
    id: 'audit_report_date',
    name: 'Audit Report Date',
    description: 'Checking if the date matches user input',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [], // Always runs
    promptKey: 'signature_date',
    category: 'Basic Compliance'
  },

  {
    id: 'standalone_consolidated_wording',
    name: 'Standalone/Consolidated Wording',
    description: 'Verifying consistency throughout document',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [
      {
        parameter: 'audit_report_type',
        operator: 'includes',
        value: ['Standalone', 'Consolidated']
      }
    ],
    promptKey: 'financial_statements_type',
    category: 'Statement Type Compliance'
  },
  {
    id: 'brsr_brr_check',
    name: 'BRSR/BRR Check',
    description: 'Verifying BRSR for top 1000/500 listed companies or BRR for others',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [
      {
        parameter: 'company_listing_status',
        operator: 'equals',
        value: 'Listed'
      }
    ],
    promptKey: 'brsr_brr',
    category: 'Sustainability Reporting'
  },
  {
    id: 'key_audit_matters',
    name: 'Key Audit Matters',
    description: 'Ensuring at least one Key Audit Matter is present',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [],
    promptKey: 'key_audit_matter',
    category: 'Audit Standards'
  },
  {
    id: 'audit_trail_software',
    name: 'Audit-Trail Accounting Software',
    description: 'Checking for required disclosure',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [],
    promptKey: 'audit_trail_software',
    category: 'Technology Compliance'
  },
  {
  id: 'profit_loss_consistency',
  name: 'Profit/Loss Consistency Check',
  description: 'Verifying if the profit/loss mentioned in audit opinion matches user selection',
  type: 'independent',
  documentTypes: ['audit_report'],
  conditions: [],
  promptKey: 'profit_loss_opinion',
  category: 'Financial Results Compliance'
},
  {
    id: 'section_197_16',
    name: 'Section 197(16)',
    description: 'Verifying presence of required disclosure',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [],
    promptKey: 'section_197_reference',
    category: 'Regulatory Compliance'
  },
  {
    id: 'company_name_consistency',
    name: 'Company Name Consistency',
    description: 'Checking for consistent company naming throughout',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [],
    promptKey: 'company_name_consistency',
    category: 'Basic Compliance'
  },
  {
    id: 'pkf_signature_block',
    name: 'PKF Signature Block',
    description: 'Verifying complete signature with registration number, partner details, UDIN',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [],
    promptKey: 'signature_date',
    category: 'Basic Compliance'
  },
  {
    id: 'benami_property_clause',
    name: 'Benami Property Clause',
    description: 'Verifying presence of Benami Property Clause in CARO annexure',
    type: 'independent',
    documentTypes: ['annexure_a'],
    conditions: [
        {
            parameter: 'audit_report_type',
            operator: 'includes',
            value: ['Standalone', 'Normal']
        }
    ],
    promptKey: 'benami_property_clause',
    category: 'CARO Regulatory Compliance'
  },
{
  id: 'caro_clause_xiii_related_party',
  name: 'CARO Clause (xiii) - Related Party Transactions Compliance',
  description: 'Verifying CARO clause (xiii) addresses related party transactions compliance with Sections 177 & 188',
  type: 'independent',
  documentTypes: ['annexure_a'],
  conditions: [
    {
      parameter: 'audit_report_type',
      operator: 'includes',
      value: ['Standalone', 'Normal']
    }
  ],
  promptKey: 'caro_clause_xiii_related_party',
  category: 'Related Party Compliance'
},

{
  id: 'caro_clause_vi_cost_auditor',
  name: 'CARO Clause (vi) - Cost Records Maintenance',
  description: 'Verifying CARO clause (vi) addresses cost records maintenance under Section 148 when Cost Auditor is appointed',
  type: 'independent',
  documentTypes: ['annexure_a'],
  conditions: [
    {
      parameter: 'audit_report_type',
      operator: 'includes',
      value: ['Standalone', 'Normal']
    },
    {
      parameter: 'has_cost_auditor',
      operator: 'equals',
      value: 'Yes',
      logic: 'AND'
    }
  ],
  promptKey: 'caro_clause_vi_cost_auditor',
  category: 'Cost Audit Compliance'
},

{
  id: 'caro_clause_vii_a_statutory_dues',
  name: 'CARO Clause (vii)(a) - Regular Deposit of Undisputed Statutory Dues',
  description: 'Verifying CARO clause (vii)(a) confirms regular deposit of undisputed statutory dues including GST, PF, ESI, Income Tax, Customs, Excise, Cess',
  type: 'independent',
  documentTypes: ['annexure_a'],
  conditions: [
    {
      parameter: 'audit_report_type',
      operator: 'includes',
      value: ['Standalone', 'Normal']
    }
  ],
  promptKey: 'caro_clause_vii_a_statutory_dues',
  category: 'Statutory Dues Compliance'
},

{
  id: 'caro_clause_vii_b_disputed_dues',
  name: 'CARO Clause (vii)(b) - Disputed Statutory Dues Disclosure',
  description: 'Verifying CARO clause (vii)(b) properly discloses disputed statutory dues status with tabular disclosure if applicable',
  type: 'independent',
  documentTypes: ['annexure_a'],
  conditions: [
    {
      parameter: 'audit_report_type',
      operator: 'includes',
      value: ['Standalone', 'Normal']
    }
  ],
  promptKey: 'caro_clause_vii_b_disputed_dues',
  category: 'Statutory Dues Compliance'
},

{
  id: 'internal_auditor_clause_xiv',
  name: 'Internal Auditor Clause (xiv)',
  description: 'Checking if CARO clause (xiv) addresses internal auditor appointment requirements',
  type: 'independent',
  documentTypes: ['annexure_a'],
  conditions: [
    {
      parameter: 'audit_report_type',
      operator: 'includes',
      value: ['Standalone', 'Normal']
    },
    {
      parameter: 'has_internal_auditor',
      operator: 'equals',
      value: 'Yes',
      logic: 'AND'
    }
  ],
  promptKey: 'internal_auditor_clause_xiv',
  category: 'Internal Auditor Compliance'
}
];

// Interlinked Document Checks (Traditional 2-document checks)
export const INTERLINKED_CHECKS: CheckDefinition[] = [
  // 2.1 Audit Report + CARO
  {
    id: 'ar_caro_reference_matching',
    name: 'Audit Report + CARO Reference Number',
    description: 'Verifying paragraph numbers match between Audit Report and CARO',
    type: 'interlinked',
    documentTypes: ['audit_report', 'annexure_a'],
    conditions: [],
    promptKey: 'caro_interlink',
    category: 'Cross-Reference Compliance'
  },

  // 2.2 Audit Report + IFC
  {
    id: 'ar_ifc_reference_matching',
    name: 'Audit Report + IFC Reference Number',
    description: 'Verifying paragraph numbers match between Audit Report and IFC',
    type: 'interlinked',
    documentTypes: ['audit_report', 'annexure_b'],
    conditions: [],
    promptKey: 'ifc_interlink',
    category: 'Cross-Reference Compliance'
  },

  // 2.3 Balance Sheet + CARO
  {
    id: 'bs_caro_ppe_check',
    name: 'Balance Sheet + CARO PPE Check',
    description: 'If PPE > 0, verifying CARO subclauses (i)(a)(A) & (i)(b)',
    type: 'interlinked',
    documentTypes: ['balance_sheet', 'annexure_a'],
    conditions: [],
    promptKey: 'bs_ppe_caro',
    category: 'Asset Verification'
  },
  {
    id: 'bs_caro_intangible_check',
    name: 'Balance Sheet + CARO Intangible Assets Check',
    description: 'If intangible assets > 0, verifying CARO subclause (i)(a)(B)',
    type: 'interlinked',
    documentTypes: ['balance_sheet', 'annexure_a'],
    conditions: [],
    promptKey: 'bs_intangible_caro',
    category: 'Asset Verification'
  },

  // add the uncertainity clause check audit_report_caro_material_uncertainty

  {
    id: 'audit_report_caro_material_uncertainty',
    name: 'Audit Report + CARO Material Uncertainty',
    description: 'Verifying if the audit report addresses material uncertainty related to going concern as per CARO requirements',
    type: 'interlinked',
    documentTypes: ['audit_report', 'annexure_a'],
    conditions: [],
    promptKey: 'audit_report_caro_material_uncertainty',
    category: 'Going Concern Compliance'
  },

  // audit_muga_s143_ifc_inadequate

  {
    id: 'audit_muga_s143_ifc_inadequate',
    name: 'Audit Report + IFC Inadequate MUGA',
    description: 'Verifying if the audit report mentions inadequate MUGA as per Section 143(3)(i) and IFC requirements',
    type: 'interlinked',
    documentTypes: ['audit_report', 'annexure_b'],
    conditions: [],
    promptKey: 'audit_muga_s143_ifc_inadequate',
    category: 'MUGA Compliance'
  },

  {
    id: 'notes_caro_adv_from_cust_deemed_deposit',
    name: 'Notes Advance from Customers + CARO (v) Deemed Deposit',
    description: 'If "Advance from Customers" in Notes changes year-on-year, CARO (v) should state no deemed deposit. If same, CARO (v) should indicate deemed deposit.',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [ // Assuming this applies to Normal/Standalone reports
      { parameter: 'audit_report_type', operator: 'includes', value: ['Normal', 'Standalone'] }
    ],
    promptKey: 'notes_caro_adv_from_cust_deemed_deposit', // This will be a placeholder, actual prompts are in multiDocumentPrompts
    category: 'Liability & Deposit Compliance'
  },

  {
    id: 'notes_caro_sebi_lodr_alignment',
    name: 'Notes SEBI LODR + CARO (iii)(a)(A) Alignment',
    description: 'Verifies if points from CARO clause (iii)(a)(A) are covered in the SEBI LODR note in Notes to Accounts. N/A if SEBI LODR note not found.',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [
      { parameter: 'audit_report_type', operator: 'includes', value: ['Normal', 'Standalone'] }
      // { parameter: 'company_listing_status', operator: 'equals', value: 'Listed' } // Consider if SEBI LODR is only for listed
    ],
    promptKey: 'notes_caro_sebi_lodr_alignment', // Placeholder
    category: 'Regulatory Compliance Alignment'
  },






  // DISABLED: Replaced by enhanced multi-document check 'inventory_goods_in_transit_check'
  // {
  //   id: 'bs_caro_inventory_check',
  //   name: 'Balance Sheet + CARO Inventory Check',
  //   description: 'If inventory > 0, verifying CARO subclause (ii)(a)',
  //   type: 'interlinked',
  //   documentTypes: ['balance_sheet', 'annexure_a', 'notes'],
  //   conditions: [],
  //   promptKey: 'bs_inventory_caro',
  //   category: 'Asset Verification',
  //   source: 'interlinked'
  // },
  {
    id: 'bs_caro_secured_borrowings',
    name: 'Balance Sheet + CARO Secured Borrowings',
    description: 'If secured borrowings > ₹5cr, verifying CARO clause (ii)(b)',
    type: 'interlinked',
    documentTypes: ['balance_sheet', 'annexure_a', 'notes'],
    conditions: [],
    promptKey: 'bs_secured_borrowings_caro',
    category: 'Liability Verification',
    source: 'interlinked'
  },
  {
    id: 'bs_caro_fixed_deposits',
    name: 'Balance Sheet + CARO Fixed Deposits',
    description: 'If fixed deposits appear in liabilities, verifying CARO clause (v)',
    type: 'interlinked',
    documentTypes: ['balance_sheet', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'bs_fixed_deposits_caro',
    category: 'Liability Verification'
  },

  // 2.4 Notes + CARO
  {
    id: 'notes_caro_immovable_property',
    name: 'Notes + CARO Immovable Property',
    description: 'If notes show immovable property, verifying CARO clause (i)(c)',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'notes_immovable_property_caro',
    category: 'Asset Verification'
  },
  {
    id: 'notes_caro_new_investments',
    name: 'Notes + CARO New Investments/Loans',
    description: 'If notes show new investments/loans/guarantees, verifying CARO clause (iii)',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'notes_investment_loans_caro',
    category: 'Investment Verification'
  },
  {
    id: 'notes_caro_contingent_liabilities',
    name: 'Notes + CARO Contingent Liabilities',
    description: 'If notes show financial guarantees, verifying CARO clauses (iii)(a)(A)+(B)',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'notes_contingent_liab_caro',
    category: 'Liability Verification'
  },

  {
    id: 'notes_caro_aggregate_threshold',
    name: 'Notes + CARO Aggregate Threshold',
    description: 'If aggregate of new loans/investments/guarantees exceeds 60% of opening net-worth, verifying CARO clause (iv)',
    type: 'interlinked',
    documentTypes: ['notes', 'balance_sheet', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'notes_aggregate_threshold_caro',
    category: 'Investment Verification'
  },
  {
    id: 'notes_caro_rpt',
    name: 'Notes + CARO Related Party Transactions',
    description: 'If notes disclose related party transactions, verifying CARO clause (iii)(f)',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'rpt_loans_caro',
    category: 'Related Party Verification'
  },
  {
    id: 'notes_caro_statutory_dues',
    name: 'Notes + CARO Statutory Dues',
    description: 'Verifying CARO clause (vii)(b) for any disputed statutory dues disclosure',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'notes_statutory_dues_caro',
    category: 'Regulatory Compliance'
  },
  {
    id: 'notes_caro_revaluation',
    name: 'Notes + CARO Revaluation',
    description: 'If notes show revaluation, verifying CARO clause (i)(d)',
    type: 'interlinked',
    documentTypes: ['notes', 'annexure_a'],
    conditions: [], // Conditional logic in processing
    promptKey: 'notes_revaluation_caro',
    category: 'Asset Verification'
  }
];

// Conditional Checks (Based on User Selections)
export const CONDITIONAL_CHECKS: CheckDefinition[] = [
  // Consolidated Report Checks
  {
    id: 'clause_21',
    name: 'Clause 21',
    description: 'Verifying Clause 21 in the Annexure A( CARO )',
    type: 'independent',
    documentTypes: ['annexure_a'],
    conditions: [
        {
        parameter: 'audit_report_type',
        operator: 'equals',
        value: 'Consolidated'
      }
    ], // Run if the Document is Consolidated
    promptKey: 'clause_21',
    category: 'CARO Consolidated'
  },

  {
    id: 'clause_20',
    name: 'CARO Clause (xx)',
    description: 'Checking if consolidated CARO annexure contains exactly one heading numbered "(xx)"',
    type: 'independent',
    documentTypes: ['annexure_a'],
    conditions: [
      {
        parameter: 'audit_report_type',
        operator: 'includes',
        value: ['Standalone', 'Normal'] // Run if the document is standalone or normal
      }
    ],
    promptKey: 'clause_20',
    category: 'CARO Standalone'
  },

  {
    id: 'consolidated_wording_consistency',
    name: 'Consolidated Statements Wording',
    description: 'Verifying consistent use of "consolidated" throughout documents',
    type: 'independent',
    documentTypes: ['audit_report'],
    conditions: [
      {
        parameter: 'audit_report_type',
        operator: 'equals',
        value: 'Consolidated'
      }
    ],
    promptKey: 'consolidated_wording',
    category: 'Consolidated Compliance'
  },

  // NBFC Specific Checks
  {
    id: 'nbfc_caro_exemptions',
    name: 'NBFC CARO Exemptions',
    description: 'For NBFCs, checking if clauses (iii)(a) & (iii)(e) are correctly omitted',
    type: 'independent',
    documentTypes: ['annexure_a'],
    conditions: [
      {
        parameter: 'is_nbfc',
        operator: 'equals',
        value: 'Yes'
      },
      {
        parameter: 'audit_report_type',
        operator: 'not_equals',
        value: 'Consolidated',
        logic: 'AND'
      }
    ],
    promptKey: 'caro_nbfc_iii_clause_present',
    category: 'NBFC Compliance'
  }
];

// Multi-Document Checks (New category for complex conditional logic)
export const MULTI_DOCUMENT_CHECKS: CheckDefinition[] = [
  {
    id: 'secretarial_caro_section192',
    name: 'Secretarial Audit Section 192 & CARO Clause (xv) Alignment',
    description: 'Cross-verify Section 192 compliance between Secretarial Audit Report and CARO',
    type: 'multi-document',
    documentTypes: ['secretarial_compliance', 'annexure_a'],
    conditions: [
      {
        parameter: 'audit_report_type',
        operator: 'includes',
        value: ['Normal', 'Standalone']
      },
      {
        parameter: 'company_listing_status',
        operator: 'equals',
        value: 'Listed',
        logic: 'AND'
      }
    ],
    promptKey: 'multi_document_section192_alignment',
    category: 'Regulatory Cross-Compliance'
  },
  {
    id: 'bs_notes_caro_investment_alignment',
    name: 'Balance Sheet + Notes + CARO Investment Disclosure Alignment',
    description: 'Cross-verify investment disclosures across three documents for consistency',
    type: 'multi-document',
    documentTypes: ['balance_sheet', 'notes', 'annexure_a'],
    conditions: [], // Always run when all three documents available
    promptKey: 'multi_document_investment_alignment',
    category: 'Investment Disclosure Alignment'
  },
  {
    id: 'audit_bs_notes_consistency',
    name: 'Audit Report + Balance Sheet + Notes Consistency Verification',
    description: 'Verify consistency of company information across primary financial documents',
    type: 'multi-document',
    documentTypes: ['audit_report', 'balance_sheet', 'notes'],
    conditions: [], // Always run when all three documents available
    promptKey: 'multi_document_consistency_check',
    category: 'Financial Statement Consistency'
  },
  {
    id: 'nbfc_multi_document_compliance',
    name: 'NBFC Specific Multi-Document Compliance Check',
    description: 'NBFC-specific compliance verification across multiple documents',
    type: 'multi-document',
    documentTypes: ['audit_report', 'annexure_a', 'balance_sheet'],
    conditions: [
      {
        parameter: 'is_nbfc',
        operator: 'equals',
        value: 'Yes'
      }
    ],
    promptKey: 'multi_document_nbfc_compliance',
    category: 'NBFC Compliance'
  }
];

// Check if conditions are met
export function shouldRunCheck(check: CheckDefinition, parameters: Record<string, any>): boolean {
  if (!check.conditions || check.conditions.length === 0) {
    return true; // Always run if no conditions
  }

  let result = true;
  let currentLogic = 'AND';

  for (const condition of check.conditions) {
    const paramValue = parameters[condition.parameter];
    let conditionMet = false;

    switch (condition.operator) {
      case 'equals':
        conditionMet = paramValue === condition.value;
        break;
      case 'not_equals':
        conditionMet = paramValue !== condition.value;
        break;
      case 'includes':
        conditionMet = Array.isArray(condition.value)
          ? condition.value.includes(paramValue)
          : String(paramValue).includes(String(condition.value));
        break;
      case 'greater_than':
        conditionMet = Number(paramValue) > Number(condition.value);
        break;
      case 'less_than':
        conditionMet = Number(paramValue) < Number(condition.value);
        break;
    }

    if (currentLogic === 'AND') {
      result = result && conditionMet;
    } else if (currentLogic === 'OR') {
      result = result || conditionMet;
    }

    currentLogic = condition.logic || 'AND';
  }

  return result;
}

// Get all checks that should run based on parameters and available documents
export function getApplicableChecks(
  parameters: Record<string, any>,
  availableDocuments: string[]
): CheckDefinition[] {
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];

  return allChecks.filter(check => {
    // Check if required documents are available
    const hasRequiredDocs = check.documentTypes.every(docType =>
      availableDocuments.includes(docType)
    );

    // Check if conditions are met
    const conditionsMet = shouldRunCheck(check, parameters);

    return hasRequiredDocs && conditionsMet;
  });
}

// Get checks by type
export function getChecksByType(type: 'independent' | 'interlinked' | 'multi-document'): CheckDefinition[] {
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];
  return allChecks.filter(check => check.type === type);
}

// Get checks by category
export function getChecksByCategory(category: string): CheckDefinition[] {
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];
  return allChecks.filter(check => check.category === category);
}

// Get all available categories
export function getAllCategories(): string[] {
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];
  const categories = new Set(allChecks.map(check => check.category || 'Uncategorized'));
  return Array.from(categories).sort();
}

// Get check statistics
export function getCheckStatistics(): {
  total: number;
  byType: Record<string, number>;
  byCategory: Record<string, number>;
  withConditions: number;
  alwaysRun: number;
} {
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];

  const byType: Record<string, number> = {};
  const byCategory: Record<string, number> = {};
  let withConditions = 0;
  let alwaysRun = 0;

  for (const check of allChecks) {
    // Count by type
    byType[check.type] = (byType[check.type] || 0) + 1;

    // Count by category
    const category = check.category || 'Uncategorized';
    byCategory[category] = (byCategory[category] || 0) + 1;

    // Count conditional vs always-run
    if (check.conditions && check.conditions.length > 0) {
      withConditions++;
    } else {
      alwaysRun++;
    }
  }

  return {
    total: allChecks.length,
    byType,
    byCategory,
    withConditions,
    alwaysRun
  };
}

// Validate check definition
export function validateCheckDefinition(check: CheckDefinition): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!check.id) errors.push('Check ID is required');
  if (!check.name) errors.push('Check name is required');
  if (!check.description) errors.push('Check description is required');
  if (!check.type) errors.push('Check type is required');
  if (!check.documentTypes || check.documentTypes.length === 0) {
    errors.push('At least one document type is required');
  }
  if (!check.promptKey) errors.push('Prompt key is required');

  // Type validation
  const validTypes = ['independent', 'interlinked', 'multi-document'];
  if (check.type && !validTypes.includes(check.type)) {
    errors.push(`Invalid check type: ${check.type}. Must be one of: ${validTypes.join(', ')}`);
  }

  // Document type validation for check type
  if (check.type === 'independent' && check.documentTypes.length > 1) {
    warnings.push('Independent checks typically use only one document type');
  }
  if (check.type === 'interlinked' && check.documentTypes.length < 2) {
    warnings.push('Interlinked checks typically require at least two document types');
  }
  if (check.type === 'multi-document' && check.documentTypes.length < 2) {
    errors.push('Multi-document checks must require at least two document types');
  }

  // Condition validation
  if (check.conditions) {
    for (const condition of check.conditions) {
      if (!condition.parameter) errors.push('Condition parameter is required');
      if (!condition.operator) errors.push('Condition operator is required');
      if (condition.value === undefined) errors.push('Condition value is required');

      const validOperators = ['equals', 'not_equals', 'includes', 'greater_than', 'less_than'];
      if (condition.operator && !validOperators.includes(condition.operator)) {
        errors.push(`Invalid condition operator: ${condition.operator}`);
      }
    }
  }

  // Category recommendation
  if (!check.category) {
    warnings.push('Consider adding a category for better organization');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Find check by ID
export function findCheckById(checkId: string): CheckDefinition | undefined {
  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];
  return allChecks.find(check => check.id === checkId);
}

// Get required documents for a set of parameters
export function getRequiredDocuments(parameters: Record<string, any>): {
  required: string[];
  recommended: string[];
  optional: string[];
} {
  const required = new Set<string>();
  const recommended = new Set<string>();
  const optional = new Set<string>();

  const allChecks = [...INDEPENDENT_CHECKS, ...INTERLINKED_CHECKS, ...CONDITIONAL_CHECKS, ...MULTI_DOCUMENT_CHECKS];

  for (const check of allChecks) {
    if (shouldRunCheck(check, parameters)) {
      // Categorize documents based on check importance
      for (const docType of check.documentTypes) {
        if (check.type === 'independent' && check.conditions.length === 0) {
          required.add(docType); // Always-run independent checks need required docs
        } else if (check.type === 'multi-document') {
          recommended.add(docType); // Multi-document checks are recommended
        } else {
          optional.add(docType); // Conditional checks are optional
        }
      }
    }
  }

  // Remove overlaps (required > recommended > optional)
  recommended.forEach(doc => required.has(doc) && recommended.delete(doc));
  optional.forEach(doc => (required.has(doc) || recommended.has(doc)) && optional.delete(doc));

  return {
    required: Array.from(required),
    recommended: Array.from(recommended),
    optional: Array.from(optional)
  };
}