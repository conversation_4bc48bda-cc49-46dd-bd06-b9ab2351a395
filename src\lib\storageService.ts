import { ref as dbRef, push, set, get, remove, update, query, orderByChild, equalTo } from "firebase/database";
import { ref as storageRef, uploadBytesResumable, getDownloadURL, deleteObject } from "firebase/storage";
import { database, storage, storageCache, addToStorageCache, getFromStorageCache } from "./firebase";
import { secureLog } from "./securityUtils";
import { CheckResult } from "./googleAI";

// Entity Types
export interface Company {
  id: string;
  name: string;
  createdAt: number;
}

export interface Partner {
  id: string;
  name: string;
  registrationNumber: string;
  createdAt: number;
}

export interface FirmSettings {
  name: string;
  registrationNumber: string;
  updatedAt: number;
}

export type AnalysisDocument = {
  type: "audit_report" | "annexure_a" | "annexure_b" | "balance_sheet" | "notes" | "pl_notes" | "annual_report" | "secretarial_compliance" | "cfs";
  file: File;
};

export type DocumentInfo = {
  id: string;
  name: string;
  type: string;
  url: string;
  path: string;
  uploadedAt: number;
  status: "uploaded" | "analyzing" | "analyzed" | "failed";
  analysisId?: string;
  stagingPath?: string; // Path to the staged copy during analysis
};

export type AnalysisParameters = {
  company_name: string;
  audit_date: Date;
  profit_or_loss: string;
  company_listing_status: string;
  top_1000_or_500: string;
  audit_report_type: string;
  audit_opinion_type: string;
  is_nbfc: string;
};

export type AnalysisResults = {
  id: string;
  userId: string;
  company_name: string;
  audit_date: Date;
  timestamp: Date;
  documents: {
    audit_report?: string;
    annexure_a?: string;
    annexure_b?: string;
    balance_sheet?: string;
    notes?: string;
    pl_notes?: string;
    annual_report?: string;
    secretarial_compliance?: string;
    cfs?: string;
  };
  parameters: AnalysisParameters;
  results: {
    [checkId: string]: CheckResult;
  };
};

// Create a cache for downloaded URLs to avoid repeated Firebase Storage calls
export const storageCache = new Map<string, {
  url: string;
  timestamp: number;
  size: number;
}>();

// Maximum cache size (100MB)
const MAX_CACHE_SIZE = 100 * 1024 * 1024;
let currentCacheSize = 0;

// Cache expiration time (30 minutes)
const CACHE_EXPIRATION = 30 * 60 * 1000;

/**
 * Add a URL to the storage cache
 */
export const addToStorageCache = (path: string, url: string, size: number = 0) => {
  // If adding this would exceed the cache size, clear old entries
  if (currentCacheSize + size > MAX_CACHE_SIZE) {
    // Sort by timestamp (oldest first)
    const entries = Array.from(storageCache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove oldest entries until we have enough space
    while (currentCacheSize + size > MAX_CACHE_SIZE && entries.length > 0) {
      const [oldestKey, oldestValue] = entries.shift()!;
      storageCache.delete(oldestKey);
      currentCacheSize -= oldestValue.size;
      console.log(`Removed ${oldestKey} from storage cache to free up space`);
    }
  }

  // Add to cache
  storageCache.set(path, {
    url,
    timestamp: Date.now(),
    size
  });

  currentCacheSize += size;
  console.log(`Added ${path} to storage cache (${size} bytes)`);
};

/**
 * Get a URL from the storage cache if it exists and is not expired
 */
export const getFromStorageCache = (path: string): string | null => {
  const cached = storageCache.get(path);

  if (!cached) {
    return null;
  }

  // Check if expired
  if (Date.now() - cached.timestamp > CACHE_EXPIRATION) {
    storageCache.delete(path);
    currentCacheSize -= cached.size;
    console.log(`Cache entry for ${path} expired and was removed`);
    return null;
  }

  console.log(`Cache hit for ${path}`);
  return cached.url;
};

/**
 * Get document URL from database by path
 */
export const getDocumentUrlFromDatabase = async (documentPath: string): Promise<string | null> => {
  try {
    console.log(`Getting document URL for path: ${documentPath}`);

    // Extract user ID from path (assuming format: users/userId/...)
    const pathParts = documentPath.split('/');
    if (pathParts.length < 3) {
      console.error(`Invalid document path format: ${documentPath}`);
      throw new Error(`Invalid document path format: ${documentPath}`);
    }

    const userId = pathParts[1]; // Second element should be the user ID
    console.log(`Extracted user ID: ${userId} from path: ${documentPath}`);

    // First check if URL exists in cache
    const cachedUrl = getFromStorageCache(documentPath);
    if (cachedUrl) {
      console.log(`Using cached URL for ${documentPath}`);
      return cachedUrl;
    }

    // Try to get a fresh URL directly from Firebase Storage
    try {
      console.log(`Attempting to get fresh URL from Firebase Storage for: ${documentPath}`);
      const fileRef = storageRef(storage, documentPath);
      const freshUrl = await getDownloadURL(fileRef);

      console.log(`Got fresh URL from Firebase Storage for: ${documentPath}`);

      // Add to cache
      addToStorageCache(documentPath, freshUrl);

      return freshUrl;
    } catch (storageError) {
      console.warn(`Could not get fresh URL from Firebase Storage: ${storageError.message}`);
      console.log(`Falling back to database lookup for URL...`);

      // If direct storage access fails, fall back to database lookup
      // Query the database to find document by path
      const documentsRef = dbRef(database, `documents/${userId}`);
      const snapshot = await get(documentsRef);

      if (!snapshot.exists()) {
        console.error(`No documents found for user: ${userId}`);
        throw new Error(`No documents found for user: ${userId}`);
      }

      // Find the document with matching path
      const documents = snapshot.val();
      for (const docId in documents) {
        if (documents[docId].path === documentPath) {
          const url = documents[docId].url;

          if (!url) {
            console.error(`Document found but URL is missing for path: ${documentPath}`);
            throw new Error(`Document URL is missing for path: ${documentPath}`);
          }

          console.log(`Found URL in database for ${documentPath}`);

          // Add URL to cache for future use
          addToStorageCache(documentPath, url, documents[docId].size || 0);

          return url;
        }
      }

      // If no document found with matching path
      console.error(`No document found with path: ${documentPath}`);
      throw new Error(`No document found with path: ${documentPath}`);
    }
  } catch (error) {
    console.error(`Error getting document URL from database: ${error}`);
    return null;
  }
};

/**
 * Upload a file to Firebase Storage
 */
export const uploadFile = async (
  file: File,
  userId: string,
  type: AnalysisDocument["type"],
  onProgress?: (progress: number) => void
): Promise<{ url: string; path: string }> => {
  try {
    // Validate file size (limit to 20MB)
    const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
    const WARNING_FILE_SIZE = 10 * 1024 * 1024; // 10MB - warn but allow

    if (file.size > MAX_FILE_SIZE) {
      throw new Error(`File size exceeds the maximum limit of 20MB. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB. Please compress the PDF before uploading.`);
    }

    if (file.size > WARNING_FILE_SIZE) {
      console.warn(`Large file detected (${(file.size / (1024 * 1024)).toFixed(2)}MB). This may cause loading issues.`);
    }

    // Create a safe filename with size info for better debugging
    const fileExtension = file.name.split(".").pop() || 'pdf';
    const timestamp = Date.now();
    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
    const safeFileName = file.name.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);

    // Include file size in the path for easier debugging
    const path = `users/${userId}/${type}_${safeFileName}_${fileSizeMB}MB_${timestamp}.${fileExtension}`;
    const fileRef = storageRef(storage, path);

    // Add metadata for better management
    const metadata = {
      contentType: file.type,
      customMetadata: {
        userId: userId,
        documentType: type,
        originalName: file.name,
        sizeBytes: file.size.toString(),
        uploadTimestamp: timestamp.toString()
      }
    };

    // Use uploadBytesResumable for progress tracking
    const uploadTask = uploadBytesResumable(fileRef, file, metadata);

    // Set up progress monitoring with retry logic
    return new Promise((resolve, reject) => {
      let lastProgress = 0;
      let progressStallCount = 0;
      let progressCheckInterval: NodeJS.Timeout | null = null;

      // Check for stalled uploads
      progressCheckInterval = setInterval(() => {
        if (lastProgress > 0 && lastProgress < 100) {
          progressStallCount++;

          // If progress is stalled for more than 30 seconds (10 checks, 3 seconds apart)
          if (progressStallCount > 10) {
            console.warn(`Upload appears to be stalled at ${lastProgress}%. Attempting to continue...`);
            // We don't cancel here, just log a warning
          }
        }
      }, 3000);

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Calculate and report progress
          const progress = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100);

          // Reset stall counter if progress is being made
          if (progress > lastProgress) {
            progressStallCount = 0;
            lastProgress = progress;
          }

          if (onProgress) {
            onProgress(progress);
          }
        },
        (error) => {
          // Handle unsuccessful uploads
          console.error("Upload error:", error);

          // Clear interval if it exists
          if (progressCheckInterval) {
            clearInterval(progressCheckInterval);
          }

          // Provide more specific error messages based on error code
          if (error.code === 'storage/unauthorized') {
            reject(new Error("You don't have permission to upload this file."));
          } else if (error.code === 'storage/canceled') {
            reject(new Error("Upload was canceled."));
          } else if (error.code === 'storage/retry-limit-exceeded') {
            reject(new Error("Upload failed due to network issues. Please check your connection and try again."));
          } else if (error.code === 'storage/invalid-checksum') {
            reject(new Error("File upload failed due to data corruption. Please try again."));
          } else {
            reject(error);
          }
        },
        async () => {
          // Handle successful upload
          try {
            // Clear interval if it exists
            if (progressCheckInterval) {
              clearInterval(progressCheckInterval);
            }

            // Get download URL with cache control and token
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

            // Add a cache-busting parameter to the URL to prevent caching issues
            const urlWithCacheBusting = downloadURL.includes('?')
              ? `${downloadURL}&_cb=${Date.now()}`
              : `${downloadURL}?_cb=${Date.now()}`;

            // Add to our URL cache
            addToStorageCache(path, downloadURL, file.size);

            resolve({ url: urlWithCacheBusting, path });
          } catch (error) {
            console.error("Error getting download URL:", error);
            reject(error);
          }
        }
      );
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    // Provide more specific error messages
    if (error instanceof Error) {
      throw new Error(`Upload failed: ${error.message}`);
    } else {
      throw new Error("Upload failed due to an unknown error");
    }
  }
};

/**
 * Upload a document and save its metadata to Realtime Database
 */
export const uploadDocument = async (
  userId: string,
  file: File,
  type: string
): Promise<string> => {
  try {
    // Upload file to storage
    const { url, path } = await uploadFile(file, userId, type as AnalysisDocument["type"]);

    // Create a new document reference
    const documentsRef = dbRef(database, `documents/${userId}`);
    const newDocRef = push(documentsRef);

    // Save document metadata
    const docData = {
      userId,
      name: file.name,
      type,
      url,
      path,
      size: file.size,
      uploadedAt: Date.now(),
      status: "uploaded"
    };

    await set(newDocRef, docData);
    return newDocRef.key || '';
  } catch (error) {
    console.error("Error uploading document:", error);
    throw error;
  }
};

/**
 * Get all documents for a user
 */
export const getUserDocuments = async (userId: string): Promise<DocumentInfo[]> => {
  try {
    const userDocsRef = dbRef(database, `documents/${userId}`);
    const snapshot = await get(userDocsRef);

    if (!snapshot.exists()) {
      return [];
    }

    const docsData = snapshot.val();
    const documents: DocumentInfo[] = [];

    // Convert the object to an array and sort by uploadedAt (descending)
    Object.keys(docsData).forEach((key) => {
      const doc = docsData[key];
      documents.push({
        id: key,
        name: doc.name,
        type: doc.type,
        url: doc.url,
        path: doc.path,
        uploadedAt: doc.uploadedAt,
        status: doc.status,
        analysisId: doc.analysisId
      });
    });

    // Sort by uploadedAt (descending)
    return documents.sort((a, b) => b.uploadedAt - a.uploadedAt);
  } catch (error) {
    console.error("Error getting user documents:", error);
    throw error;
  }
};

/**
 * Delete a document
 */
export const deleteDocument = async (userId: string, documentId: string): Promise<void> => {
  try {
    // Get the document data first
    const docRef = dbRef(database, `documents/${userId}/${documentId}`);
    const snapshot = await get(docRef);

    if (!snapshot.exists()) {
      throw new Error("Document not found");
    }

    const documentData = snapshot.val();

    // Delete from storage
    const fileRef = storageRef(storage, documentData.path);
    await deleteObject(fileRef);

    // Delete from Realtime Database
    await remove(docRef);

    // Remove from URL cache if exists
    if (storageCache.has(documentData.path)) {
      const cachedSize = storageCache.get(documentData.path)?.size || 0;
      storageCache.delete(documentData.path);
      currentCacheSize -= cachedSize;
      console.log(`Removed ${documentData.path} from storage cache after deletion`);
    }
  } catch (error) {
    console.error("Error deleting document:", error);
    throw error;
  }
};

/**
 * Get available documents for analysis
 */
export const getAvailableDocumentsForAnalysis = async (userId: string): Promise<{
  auditReports: DocumentInfo[];
  annexureA: DocumentInfo[];
  annexureB: DocumentInfo[];
  balanceSheet: DocumentInfo[];
  notes: DocumentInfo[];
  plNotes: DocumentInfo[];
  annualReports: DocumentInfo[];
  secretarialCompliance: DocumentInfo[];
}> => {
  try {
    const allDocs = await getUserDocuments(userId);

    // Filter documents by type only (allow both uploaded and analyzed documents)
    const auditReports = allDocs.filter(doc => doc.type === "audit_report");
    const annexureA = allDocs.filter(doc => doc.type === "annexure_a");
    const annexureB = allDocs.filter(doc => doc.type === "annexure_b");
    const balanceSheet = allDocs.filter(doc => doc.type === "balance_sheet");
    const notes = allDocs.filter(doc => doc.type === "notes");
    const plNotes = allDocs.filter(doc => doc.type === "pl_notes");
    const annualReports = allDocs.filter(doc => doc.type === "annual_report");
    const secretarialCompliance = allDocs.filter(doc => doc.type === "secretarial_compliance");

    return { auditReports, annexureA, annexureB, balanceSheet, notes, plNotes, annualReports, secretarialCompliance };
  } catch (error) {
    console.error("Error getting available documents:", error);
    throw error;
  }
};

/**
 * Create a staging copy of a document for analysis
 */
export const stageDocumentForAnalysis = async (userId: string, documentId: string): Promise<string> => {
  try {
    // Get the document data
    const docRef = dbRef(database, `documents/${userId}/${documentId}`);
    const snapshot = await get(docRef);

    if (!snapshot.exists()) {
      throw new Error("Document not found");
    }

    const documentData = snapshot.val();

    // Just use the original path but mark the document as "analyzing"
    const stagingPath = documentData.path;

    // Update document status to "analyzing"
    await update(docRef, {
      status: "analyzing",
      stagingPath: stagingPath
    });

    console.log(`Document ${documentId} marked as analyzing. Using path: ${stagingPath}`);

    return stagingPath;
  } catch (error) {
    console.error("Error staging document for analysis:", error);
    throw error;
  }
};

/**
 * Update document status after analysis
 */
export const updateDocumentStatus = async (
  userId: string,
  documentId: string,
  analysisId: string,
  success: boolean = true
): Promise<void> => {
  try {
    const docRef = dbRef(database, `documents/${userId}/${documentId}`);

    // Check if the document exists
    const snapshot = await get(docRef);
    if (!snapshot.exists()) {
      throw new Error("Document not found");
    }

    // If analysis was successful
    if (success) {
      await update(docRef, {
        status: "analyzed",
        analysisId,
        // Remove staging path if it exists
        stagingPath: null
      });
    } else {
      // If analysis failed
      await update(docRef, {
        status: "failed",
        // Keep the staging path for debugging
        analysisId
      });
    }
  } catch (error) {
    console.error("Error updating document status:", error);
    throw error;
  }
};

/**
 * Save analysis results to Realtime Database
 */
export const saveAnalysisResults = async (
  userId: string,
  parameters: AnalysisParameters,
  documents: {
    audit_report?: { url: string; id: string; name: string; path: string };
    annexure_a?: { url: string; id: string; name: string; path: string };
    annexure_b?: { url: string; id: string; name: string; path: string };
    balance_sheet?: { url: string; id: string; name: string; path: string };
    notes?: { url: string; id: string; name: string; path: string };
    pl_notes?: { url: string; id: string; name: string; path: string };
    annual_report?: { url: string; id: string; name: string; path: string };
    secretarial_compliance?: { url: string; id: string; name: string; path: string };
    cfs?: { url: string; id: string; name: string; path: string };
  },
  results: {
    [checkId: string]: CheckResult;
  }
): Promise<string> => {
  try {
    // Create a new analysis results reference
    const analysisRef = dbRef(database, `analysis_results/${userId}`);
    const newAnalysisRef = push(analysisRef);

    // Convert Date objects to timestamps for storage and ensure no undefined values
    // Create documents object with only defined values
    const documentsObj: Record<string, any> = {};
    if (documents.audit_report?.url) {
      documentsObj.audit_report = {
        url: documents.audit_report.url,
        name: documents.audit_report.name,
        path: documents.audit_report.path
      };
    }
    if (documents.annexure_a?.url) {
      documentsObj.annexure_a = {
        url: documents.annexure_a.url,
        name: documents.annexure_a.name,
        path: documents.annexure_a.path
      };
    }
    if (documents.annexure_b?.url) {
      documentsObj.annexure_b = {
        url: documents.annexure_b.url,
        name: documents.annexure_b.name,
        path: documents.annexure_b.path
      };
    }
    if (documents.balance_sheet?.url) {
      documentsObj.balance_sheet = {
        url: documents.balance_sheet.url,
        name: documents.balance_sheet.name,
        path: documents.balance_sheet.path
      };
    }
    if (documents.notes?.url) {
      documentsObj.notes = {
        url: documents.notes.url,
        name: documents.notes.name,
        path: documents.notes.path
      };
    }
    if (documents.pl_notes?.url) {
      documentsObj.pl_notes = {
        url: documents.pl_notes.url,
        name: documents.pl_notes.name,
        path: documents.pl_notes.path
      };
    }
    if (documents.annual_report?.url) {
      documentsObj.annual_report = {
        url: documents.annual_report.url,
        name: documents.annual_report.name,
        path: documents.annual_report.path
      };
    }
    if (documents.secretarial_compliance?.url) {
      documentsObj.secretarial_compliance = {
        url: documents.secretarial_compliance.url,
        name: documents.secretarial_compliance.name,
        path: documents.secretarial_compliance.path
      };
    }
    if (documents.cfs?.url) {
      documentsObj.cfs = {
        url: documents.cfs.url,
        name: documents.cfs.name,
        path: documents.cfs.path
      };
    }

    // Create documentIds object with only defined values
    const documentIdsObj: Record<string, string> = {};
    if (documents.audit_report?.id) documentIdsObj.audit_report = documents.audit_report.id;
    if (documents.annexure_a?.id) documentIdsObj.annexure_a = documents.annexure_a.id;
    if (documents.annexure_b?.id) documentIdsObj.annexure_b = documents.annexure_b.id;
    if (documents.balance_sheet?.id) documentIdsObj.balance_sheet = documents.balance_sheet.id;
    if (documents.notes?.id) documentIdsObj.notes = documents.notes.id;
    if (documents.pl_notes?.id) documentIdsObj.pl_notes = documents.pl_notes.id;
    if (documents.annual_report?.id) documentIdsObj.annual_report = documents.annual_report.id;
    if (documents.secretarial_compliance?.id) documentIdsObj.secretarial_compliance = documents.secretarial_compliance.id;
    if (documents.cfs?.id) documentIdsObj.cfs = documents.cfs.id;

    // Sanitize results to ensure no undefined values (Firebase doesn't accept undefined)
    const sanitizedResults: Record<string, any> = {};

    for (const [key, value] of Object.entries(results)) {
      // Create a sanitized copy of each result
      const sanitizedResult: Record<string, any> = {
        isCompliant: value.isCompliant || false,
        explanation: value.explanation || "",
        confidence: value.confidence || 0.5,
        detail: value.detail || "",
        extractedData: value.extractedData || null
      };

      // Add any other properties that might exist
      if (value.evidence) sanitizedResult.evidence = value.evidence;
      if (value.source) sanitizedResult.source = value.source;

      sanitizedResults[key] = sanitizedResult;
    }

    const analysisData = {
      userId,
      company_name: parameters.company_name,
      audit_date: parameters.audit_date.getTime(),
      timestamp: Date.now(),
      documents: documentsObj,
      documentIds: documentIdsObj,
      parameters: {
        ...parameters,
        audit_date: parameters.audit_date.getTime()
      },
      results: sanitizedResults
    };

    await set(newAnalysisRef, analysisData);
    const analysisId = newAnalysisRef.key || '';

    // Update document statuses - only for documents that have both id and url
    // Mark all documents as successfully analyzed
    try {
      if (documents.audit_report?.id) {
        await updateDocumentStatus(userId, documents.audit_report.id, analysisId, true);
      }
      if (documents.annexure_a?.id) {
        await updateDocumentStatus(userId, documents.annexure_a.id, analysisId, true);
      }
      if (documents.annexure_b?.id) {
        await updateDocumentStatus(userId, documents.annexure_b.id, analysisId, true);
      }
      if (documents.balance_sheet?.id) {
        await updateDocumentStatus(userId, documents.balance_sheet.id, analysisId, true);
      }
      if (documents.notes?.id) {
        await updateDocumentStatus(userId, documents.notes.id, analysisId, true);
      }
      if (documents.pl_notes?.id) {
        await updateDocumentStatus(userId, documents.pl_notes.id, analysisId, true);
      }
      if (documents.annual_report?.id) {
        await updateDocumentStatus(userId, documents.annual_report.id, analysisId, true);
      }
      if (documents.secretarial_compliance?.id) {
        await updateDocumentStatus(userId, documents.secretarial_compliance.id, analysisId, true);
      }
      if (documents.cfs?.id) {
        await updateDocumentStatus(userId, documents.cfs.id, analysisId, true);
      }
    } catch (error) {
      console.error("Error updating document statuses:", error);
      // Continue with the analysis result even if status updates fail
    }

    return analysisId;
  } catch (error) {
    console.error("Error saving analysis results:", error);
    throw error;
  }
};

/**
 * Get analysis history for a user
 */
export const getAnalysisHistory = async (userId: string): Promise<AnalysisResults[]> => {
  try {
    secureLog(`Fetching analysis history for user:`, { userId: '[REDACTED]' });

    const userAnalysesRef = dbRef(database, `analysis_results/${userId}`);
    const snapshot = await get(userAnalysesRef);

    if (!snapshot.exists()) {
      return [];
    }

    const analysesData = snapshot.val();
    const analyses: AnalysisResults[] = [];

    // Convert the object to an array and sort by timestamp (descending)
    Object.keys(analysesData).forEach((key) => {
      const analysis = analysesData[key];
      analyses.push({
        id: key,
        userId: analysis.userId,
        company_name: analysis.company_name,
        audit_date: new Date(analysis.audit_date),
        timestamp: new Date(analysis.timestamp),
        documents: analysis.documents,
        parameters: analysis.parameters,
        results: analysis.results
      });
    });

    // Sort by timestamp (descending - newest first)
    return analyses.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  } catch (error) {
    secureLog("Error getting analysis history:", error);
    throw error;
  }
};

/**
 * Get a single analysis result by ID
 */
export const getAnalysisById = async (analysisId: string, userId: string): Promise<AnalysisResults | null> => {
  try {
    console.log(`Fetching analysis with ID: ${analysisId} for user: ${userId}`);
    const analysisRef = dbRef(database, `analysis_results/${userId}/${analysisId}`);
    const snapshot = await get(analysisRef);

    if (!snapshot.exists()) {
      console.warn(`Analysis with ID ${analysisId} not found for user ${userId}`);
      return null;
    }

    const data = snapshot.val();
    console.log(`Raw data for analysis ${analysisId}:`, data);

    // Handle missing or malformed data with defaults
    const safeData = {
      id: analysisId,
      userId: data.userId || userId,
      company_name: data.company_name || "Unknown Company",
      audit_date: data.audit_date ? new Date(data.audit_date) : new Date(),
      timestamp: data.timestamp ? new Date(data.timestamp) : new Date(),
      documents: data.documents || {},
      parameters: {
        company_name: (data.parameters?.company_name || data.company_name || "Unknown Company"),
        audit_date: data.parameters?.audit_date ? new Date(data.parameters.audit_date) : new Date(),
        profit_or_loss: data.parameters?.profit_or_loss || "Profit",
        company_listing_status: data.parameters?.company_listing_status || "Unlisted",
        top_1000_or_500: data.parameters?.top_1000_or_500 || "No",
        audit_report_type: data.parameters?.audit_report_type || "Normal",
        audit_opinion_type: data.parameters?.audit_opinion_type || "Unmodified",
        is_nbfc: data.parameters?.is_nbfc || "No"
      },
      results: data.results || {}
    };

    console.log(`Processed data for analysis ${analysisId}:`, safeData);
    return safeData;
  } catch (error) {
    console.error(`Error getting analysis by ID ${analysisId}:`, error);
    throw error;
  }
};

// Company Management Functions

/**
 * Add a new company
 */
export const addCompany = async (userId: string, name: string): Promise<Company> => {
  try {
    const companiesRef = dbRef(database, `companies/${userId}`);
    const newCompanyRef = push(companiesRef);
    const companyId = newCompanyRef.key || '';

    const companyData: Omit<Company, 'id'> = {
      name,
      createdAt: Date.now()
    };

    await set(newCompanyRef, companyData);

    return {
      id: companyId,
      ...companyData
    };
  } catch (error) {
    console.error("Error adding company:", error);
    throw error;
  }
};

/**
 * Get all companies for a user
 */
export const getCompanies = async (userId: string): Promise<Company[]> => {
  try {
    const companiesRef = dbRef(database, `companies/${userId}`);
    const snapshot = await get(companiesRef);

    if (!snapshot.exists()) {
      return [];
    }

    const companiesData = snapshot.val();
    const companies: Company[] = [];

    Object.keys(companiesData).forEach((key) => {
      companies.push({
        id: key,
        name: companiesData[key].name,
        createdAt: companiesData[key].createdAt
      });
    });

    // Sort by name
    return companies.sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error("Error getting companies:", error);
    throw error;
  }
};

/**
 * Update a company
 */
export const updateCompany = async (userId: string, companyId: string, name: string): Promise<void> => {
  try {
    const companyRef = dbRef(database, `companies/${userId}/${companyId}`);
    await update(companyRef, { name });
  } catch (error) {
    console.error("Error updating company:", error);
    throw error;
  }
};

/**
 * Delete a company
 */
export const deleteCompany = async (userId: string, companyId: string): Promise<void> => {
  try {
    const companyRef = dbRef(database, `companies/${userId}/${companyId}`);
    await remove(companyRef);
  } catch (error) {
    console.error("Error deleting company:", error);
    throw error;
  }
};

// Partner Management Functions

/**
 * Add a new partner
 */
export const addPartner = async (userId: string, name: string, registrationNumber: string): Promise<Partner> => {
  try {
    const partnersRef = dbRef(database, `partners/${userId}`);
    const newPartnerRef = push(partnersRef);
    const partnerId = newPartnerRef.key || '';

    const partnerData: Omit<Partner, 'id'> = {
      name,
      registrationNumber,
      createdAt: Date.now()
    };

    await set(newPartnerRef, partnerData);

    return {
      id: partnerId,
      ...partnerData
    };
  } catch (error) {
    console.error("Error adding partner:", error);
    throw error;
  }
};

/**
 * Get all partners for a user
 */
/**
 * Get all partners for a user
 */
export const getPartners = async (userId: string): Promise<Partner[]> => {
  try {
    const partnersRef = dbRef(database, `partners/${userId}`);
    const snapshot = await get(partnersRef);

    if (!snapshot.exists()) {
      return [];
    }

    const partnersData = snapshot.val();
    const partners: Partner[] = [];

    Object.keys(partnersData).forEach((key) => {
      partners.push({
        id: key,
        name: partnersData[key].name,
        registrationNumber: partnersData[key].registrationNumber || '',
        createdAt: partnersData[key].createdAt
      });
    });

    // Sort by name
    return partners.sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error("Error getting partners:", error);
    throw error;
  }
};

/**
 * Update a partner
 */
export const updatePartner = async (
  userId: string,
  partnerId: string,
  name: string,
  registrationNumber: string
): Promise<void> => {
  try {
    const partnerRef = dbRef(database, `partners/${userId}/${partnerId}`);
    await update(partnerRef, { name, registrationNumber });
  } catch (error) {
    console.error("Error updating partner:", error);
    throw error;
  }
};

/**
 * Delete a partner
 */
export const deletePartner = async (userId: string, partnerId: string): Promise<void> => {
  try {
    const partnerRef = dbRef(database, `partners/${userId}/${partnerId}`);
    await remove(partnerRef);
  } catch (error) {
    console.error("Error deleting partner:", error);
    throw error;
  }
};

// Firm Settings Management Functions

/**
 * Get firm settings
 */
export const getFirmSettings = async (userId: string): Promise<FirmSettings | null> => {
  try {
    const firmRef = dbRef(database, `firm_settings/${userId}`);
    const snapshot = await get(firmRef);

    if (!snapshot.exists()) {
      return null;
    }

    return snapshot.val();
  } catch (error) {
    console.error("Error getting firm settings:", error);
    throw error;
  }
};

/**
 * Save firm settings
 */
export const saveFirmSettings = async (
  userId: string,
  name: string,
  registrationNumber: string
): Promise<FirmSettings> => {
  try {
    const firmRef = dbRef(database, `firm_settings/${userId}`);

    const firmData: FirmSettings = {
      name,
      registrationNumber,
      updatedAt: Date.now()
    };

    await set(firmRef, firmData);

    return firmData;
  } catch (error) {
    console.error("Error saving firm settings:", error);
    throw error;
  }
};

/**
 * Refresh document URL
 * This function gets a fresh download URL for a document
 */
export const refreshDocumentUrl = async (userId: string, documentId: string): Promise<string> => {
  try {
    // Get document data
    const docRef = dbRef(database, `documents/${userId}/${documentId}`);
    const snapshot = await get(docRef);

    if (!snapshot.exists()) {
      throw new Error("Document not found");
    }

    const documentData = snapshot.val();
    const path = documentData.path;

    // Get fresh download URL from Firebase Storage
    const fileRef = storageRef(storage, path);
    const newUrl = await getDownloadURL(fileRef);

    // Add cache-busting parameter
    const urlWithCacheBusting = newUrl.includes('?')
      ? `${newUrl}&_cb=${Date.now()}`
      : `${newUrl}?_cb=${Date.now()}`;

    // Update URL in database
    await update(docRef, {
      url: urlWithCacheBusting,
      urlRefreshedAt: Date.now()
    });

    // Update cache
    addToStorageCache(path, newUrl, documentData.size || 0);

    console.log(`Refreshed URL for document ${documentId}`);
    return urlWithCacheBusting;
  } catch (error) {
    console.error("Error refreshing document URL:", error);
    throw error;
  }
};

/**
 * Download document by ID
 * This is a convenience function to get the document and its URL in one call
 */
export const downloadDocumentById = async (userId: string, documentId: string): Promise<{
  url: string;
  name: string;
  type: string;
}> => {
  try {
    // Get document data
    const docRef = dbRef(database, `documents/${userId}/${documentId}`);
    const snapshot = await get(docRef);

    if (!snapshot.exists()) {
      throw new Error("Document not found");
    }

    const documentData = snapshot.val();

    // Check if URL needs refreshing (older than 1 hour)
    if (!documentData.urlRefreshedAt ||
        (Date.now() - documentData.urlRefreshedAt > 60 * 60 * 1000)) {
      // Refresh the URL if it's old
      const newUrl = await refreshDocumentUrl(userId, documentId);

      return {
        url: newUrl,
        name: documentData.name,
        type: documentData.type
      };
    }

    // Return existing URL
    return {
      url: documentData.url,
      name: documentData.name,
      type: documentData.type
    };
  } catch (error) {
    console.error("Error downloading document:", error);
    throw error;
  }
};

/**
 * Check file existence and metadata
 */
export const checkFileExists = async (path: string): Promise<{
  exists: boolean;
  size?: number;
  error?: string;
  lastModified?: number;
  url?: string;
}> => {
  try {
    console.log(`Checking if file exists: ${path}`);

    // Try to get metadata from Firebase Storage
    const fileRef = storageRef(storage, path);

    try {
      // First try to get the download URL
      const downloadUrl = await getDownloadURL(fileRef);
      console.log(`File exists, got download URL for: ${path}`);

      // Add to cache for future use
      addToStorageCache(path, downloadUrl);

      // If we can get URL, try to get metadata too
      try {
        // Try to get the blob directly to verify file exists and get size
        const { getBlob } = await import("firebase/storage");
        const blob = await getBlob(fileRef);

        if (!blob || blob.size === 0) {
          console.warn(`File exists but blob is empty for ${path}`);
          return {
            exists: true,
            size: 0,
            url: downloadUrl,
            error: "File exists but is empty"
          };
        }

        console.log(`Got blob for ${path}: size=${blob.size} bytes`);

        return {
          exists: true,
          size: blob.size,
          url: downloadUrl
        };
      } catch (blobErr) {
        console.warn(`Could not get blob for ${path}, trying HTTP HEAD request`, blobErr);

        // Fall back to HTTP HEAD request if blob fetch fails
        try {
          // Add cache busting to avoid cached responses
          const urlWithCacheBusting = downloadUrl.includes('?')
            ? `${downloadUrl}&_cb=${Date.now()}`
            : `${downloadUrl}?_cb=${Date.now()}`;

          const meta = await fetch(urlWithCacheBusting, {
            method: 'HEAD',
          });

          const size = parseInt(meta.headers.get('Content-Length') || '0');
          const lastModified = new Date(meta.headers.get('Last-Modified') || '').getTime();

          console.log(`Got metadata for ${path}: size=${size}, lastModified=${new Date(lastModified).toISOString()}`);

          return {
            exists: true,
            size,
            lastModified,
            url: downloadUrl
          };
        } catch (metaErr) {
          // If metadata fetch fails, file still exists
          console.warn(`Could not fetch metadata for ${path}, but file exists`, metaErr);
          return {
            exists: true,
            url: downloadUrl
          };
        }
      }
    } catch (error) {
      console.error("Error checking file existence:", error);

      // Try to get the document from the database as a fallback
      try {
        // Extract user ID from path (assuming format: users/userId/...)
        const pathParts = path.split('/');
        if (pathParts.length >= 3) {
          const userId = pathParts[1];
          console.log(`Trying to find document in database for user ${userId} with path ${path}`);

          // Query the database to find document by path
          const documentsRef = dbRef(database, `documents/${userId}`);
          const snapshot = await get(documentsRef);

          if (snapshot.exists()) {
            const documents = snapshot.val();
            for (const docId in documents) {
              if (documents[docId].path === path) {
                console.log(`Found document in database with path ${path}`);

                // Try to get a fresh URL
                try {
                  const freshUrl = await getDownloadURL(fileRef);
                  console.log(`Got fresh URL for ${path}`);

                  return {
                    exists: true,
                    url: freshUrl
                  };
                } catch (urlErr) {
                  console.warn(`Could not get fresh URL, using stored URL`);

                  // Use the URL from the database
                  if (documents[docId].url) {
                    return {
                      exists: true,
                      url: documents[docId].url
                    };
                  }
                }
              }
            }
          }
        }
      } catch (dbError) {
        console.error(`Error checking database for document:`, dbError);
      }

      // Provide more specific error messages based on error code
      let errorMessage = '';
      if (error.code === 'storage/object-not-found') {
        errorMessage = 'File not found in storage';
      } else if (error.code === 'storage/unauthorized') {
        errorMessage = 'Not authorized to access this file';
      } else if (error.code === 'storage/canceled') {
        errorMessage = 'File access was canceled';
      } else if (error.code === 'storage/unknown') {
        errorMessage = 'Unknown error accessing file';
      } else {
        errorMessage = `Error accessing file: ${error.message}`;
      }

      return {
        exists: false,
        error: errorMessage
      };
    }
  } catch (error) {
    console.error("Error in checkFileExists:", error);
    return {
      exists: false,
      error: `Error checking file: ${error instanceof Error ? error.message : String(error)}`
    };
  }
};

/**
 * Get document by path
 */
export const getDocumentByPath = async (path: string): Promise<DocumentInfo | null> => {
  try {
    // Extract user ID from path (assuming format: users/userId/...)
    const pathParts = path.split('/');
    if (pathParts.length < 3) {
      throw new Error(`Invalid document path format: ${path}`);
    }

    const userId = pathParts[1];

    // Get all user documents
    const userDocsRef = dbRef(database, `documents/${userId}`);
    const snapshot = await get(userDocsRef);

    if (!snapshot.exists()) {
      return null;
    }

    const docsData = snapshot.val();

    // Find document with matching path
    for (const docId in docsData) {
      if (docsData[docId].path === path) {
        return {
          id: docId,
          ...docsData[docId]
        };
      }
    }

    return null;
  } catch (error) {
    console.error("Error getting document by path:", error);
    return null;
  }
};

/**
 * Repair document URLs
 * This function finds documents with broken URLs and refreshes them
 */
export const repairDocumentUrls = async (userId: string): Promise<{
  repaired: number;
  failed: number;
  total: number;
}> => {
  try {
    const userDocs = await getUserDocuments(userId);
    let repaired = 0;
    let failed = 0;

    console.log(`Checking ${userDocs.length} documents for URL repair`);

    for (const doc of userDocs) {
      try {
        // Check if URL works
        const response = await fetch(doc.url, {
          method: 'HEAD',
          mode: 'no-cors'
        }).catch(() => null);

        if (!response) {
          console.log(`Document ${doc.id} has broken URL, attempting repair`);

          // Try to refresh URL
          await refreshDocumentUrl(userId, doc.id);
          repaired++;
          console.log(`Repaired URL for document ${doc.id}`);
        }
      } catch (error) {
        console.error(`Failed to repair URL for document ${doc.id}:`, error);
        failed++;
      }
    }

    return {
      repaired,
      failed,
      total: userDocs.length
    };
  } catch (error) {
    console.error("Error repairing document URLs:", error);
    throw error;
  }
};