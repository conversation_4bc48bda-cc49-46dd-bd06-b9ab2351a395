// Firebase Connection Test
import { database } from './firebase';
import { ref, get } from 'firebase/database';

export async function testFirebaseConnection() {
  try {
    console.log('🔥 Testing Firebase connection...');
    
    // Test basic connection
    const testRef = ref(database, '.info/connected');
    const snapshot = await get(testRef);
    
    console.log('🔥 Firebase connected:', snapshot.val());
    
    // Test reading from analyses
    const analysesRef = ref(database, 'analyses');
    const analysesSnapshot = await get(analysesRef);
    
    console.log('🔥 Analyses exist:', analysesSnapshot.exists());
    console.log('🔥 Analyses data:', analysesSnapshot.val());
    
    return {
      connected: snapshot.val(),
      hasAnalyses: analysesSnapshot.exists(),
      analysesData: analysesSnapshot.val()
    };
  } catch (error) {
    console.error('🔥 Firebase connection test failed:', error);
    throw error;
  }
}
