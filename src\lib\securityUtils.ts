// securityUtils.ts - Security utilities for authentication and data protection

import { QueryClient } from '@tanstack/react-query';

/**
 * Clear React Query cache safely
 */
export const clearReactQueryCache = (queryClient?: QueryClient) => {
  try {
    if (queryClient) {
      queryClient.clear();
    } else if (typeof window !== 'undefined' && window.__REACT_QUERY_CACHE__) {
      window.__REACT_QUERY_CACHE__.clear();
    }
  } catch (error) {
    console.warn('Error clearing React Query cache:', error);
  }
};

/**
 * Clear all browser storage related to authentication
 */
export const clearAuthStorage = () => {
  try {
    // Clear localStorage
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('firebase:') || 
        key.includes('token') || 
        key.includes('auth') ||
        key.includes('session') ||
        key.includes('user') ||
        key.includes('cache')
      )) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear sessionStorage completely
    sessionStorage.clear();

    // Clear any IndexedDB data if exists
    if ('indexedDB' in window) {
      indexedDB.databases().then(databases => {
        databases.forEach(db => {
          if (db.name && (
            db.name.includes('firebase') || 
            db.name.includes('auth') || 
            db.name.includes('cache')
          )) {
            indexedDB.deleteDatabase(db.name);
          }
        });
      }).catch(() => {
        // Ignore errors for IndexedDB cleanup
      });
    }

    // Clear cookies related to authentication
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
      if (name.includes('auth') || name.includes('session') || name.includes('token')) {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      }
    });

    // Clear in-memory cache
    if (typeof window !== 'undefined' && window.storageCache) {
      window.storageCache.clear();
    }

    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  } catch (error) {
    console.warn('Error clearing auth storage:', error);
  }
};

/**
 * Sanitize sensitive data from console logs
 */
export const sanitizeLogData = (data: any): any => {
  if (typeof data === 'object' && data !== null) {
    const sanitized = { ...data };
    const sensitiveKeys = ['token', 'auth', 'password', 'secret', 'key', 'uid', 'email'];
    
    Object.keys(sanitized).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = sanitizeLogData(sanitized[key]);
      }
    });
    
    return sanitized;
  }
  return data;
};

/**
 * Check if the current environment is production
 */
export const isProduction = () => {
  return import.meta.env.PROD || 
         window.location.hostname === 'localhost' || 
         window.location.hostname === '127.0.0.1';
};

/**
 * Secure console logging (only in development)
 */
export const secureLog = (message: string, data?: any) => {
  if (!isProduction()) {
    if (data) {
      console.log(message, sanitizeLogData(data));
    } else {
      console.log(message);
    }
  }
};

/**
 * Validate Firebase configuration security
 */
export const validateFirebaseConfig = () => {
  const requiredEnvVars = [
    'VITE_FIREBASE_API_KEY',
    'VITE_FIREBASE_AUTH_DOMAIN',
    'VITE_FIREBASE_DATABASE_URL',
    'VITE_FIREBASE_PROJECT_ID',
    'VITE_FIREBASE_STORAGE_BUCKET',
    'VITE_FIREBASE_MESSAGING_SENDER_ID',
    'VITE_FIREBASE_APP_ID'
  ];

  const missingVars = requiredEnvVars.filter(varName => !import.meta.env[varName]);

  if (missingVars.length > 0) {
    console.error('Missing required Firebase environment variables:', missingVars);
    return false;
  }

  // Validate that we're using the correct project
  const projectId = import.meta.env.VITE_FIREBASE_PROJECT_ID;
  if (!projectId || projectId === 'your_project_id') {
    console.error('Firebase project ID not properly configured');
    return false;
  }

  return true;
};

/**
 * Check if the current domain is authorized
 */
export const validateDomain = () => {
  const allowedDomains = [
    'checklistaicap.firebaseapp.com',
    'checklistaicap.web.app',
    'localhost',
    '127.0.0.1',
    'pkfaudit.ai' // Add your custom domain if you have one
  ];

  const currentDomain = window.location.hostname;
  const isAllowed = allowedDomains.some(domain =>
    currentDomain === domain || currentDomain.endsWith(`.${domain}`)
  );

  if (!isAllowed && isProduction()) {
    console.warn('Domain validation failed for:', currentDomain);
    // Don't block in production, just log the warning
    return true;
  }

  return true;
};

/**
 * Initialize security checks
 */
export const initializeSecurity = () => {
  try {
    // Validate Firebase configuration
    if (!validateFirebaseConfig()) {
      console.warn('Firebase configuration validation failed');
      // Don't throw error, just warn
    }

    // Validate domain
    if (!validateDomain()) {
      console.warn('Domain validation failed');
      // Don't throw error, just warn
    }

    // Only apply restrictive measures in production and not during authentication
    if (isProduction() && !window.location.pathname.includes('/login')) {
      // Disable right-click in production (but not on login page)
      document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
      });

      // Disable F12, Ctrl+Shift+I, Ctrl+U (but not on login page)
      document.addEventListener('keydown', (e) => {
        if (
          e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.key === 'u')
        ) {
          e.preventDefault();
        }
      });
    }

    secureLog('Security initialization completed');
  } catch (error) {
    console.warn('Security initialization warning:', error);
    // Don't block the app, just log warnings
  }
};

/**
 * Set Content Security Policy headers
 */
export const setCSPHeaders = () => {
  // Comprehensive CSP for Firebase and Microsoft authentication
  const csp = [
    "default-src 'self'",
    // Script sources - Allow Firebase, Google APIs, Microsoft, and inline scripts
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' " +
    "https://apis.google.com " +
    "https://www.gstatic.com " +
    "https://securetoken.googleapis.com " +
    "https://identitytoolkit.googleapis.com " +
    "https://www.googleapis.com " +
    "https://accounts.google.com " +
    "https://login.microsoftonline.com " +
    "https://logincdn.msauth.net " +
    "https://logincdn.msftauth.net " +
    "https://js.monitor.azure.com " +
    "https://www.google.com " +
    "https://ssl.gstatic.com " +
    "https://checklistaicap.firebaseapp.com " +
    "https://checklistaicap-default-rtdb.firebaseio.com",

    // Style sources - Allow inline styles and external stylesheets
    "style-src 'self' 'unsafe-inline' " +
    "https://fonts.googleapis.com " +
    "https://accounts.google.com " +
    "https://login.microsoftonline.com " +
    "https://logincdn.msauth.net " +
    "https://logincdn.msftauth.net",

    // Font sources
    "font-src 'self' " +
    "https://fonts.gstatic.com " +
    "https://logincdn.msauth.net " +
    "https://logincdn.msftauth.net",

    // Image sources - Allow all HTTPS, data URLs, and blob URLs
    "img-src 'self' data: https: blob: " +
    "https://accounts.google.com " +
    "https://login.microsoftonline.com " +
    "https://logincdn.msauth.net " +
    "https://logincdn.msftauth.net " +
    "https://secure.gravatar.com " +
    "https://lh3.googleusercontent.com",

    // Connect sources - Allow Firebase, Google APIs, Microsoft, and WebSocket connections
    "connect-src 'self' " +
    "https://identitytoolkit.googleapis.com " +
    "https://securetoken.googleapis.com " +
    "https://checklistaicap-default-rtdb.firebaseio.com " +
    "https://firestore.googleapis.com " +
    "https://www.googleapis.com " +
    "https://accounts.google.com " +
    "https://login.microsoftonline.com " +
    "https://logincdn.msauth.net " +
    "https://logincdn.msftauth.net " +
    "https://graph.microsoft.com " +
    "https://graph.windows.net " +
    "https://management.azure.com " +
    "https://checklistaicap.firebasestorage.app " +
    "https://storage.googleapis.com " +
    "https://generativelanguage.googleapis.com " +
    "wss: ws:",

    // Frame sources - Allow Firebase and Microsoft authentication frames
    "frame-src 'self' " +
    "https://checklistaicap.firebaseapp.com " +
    "https://accounts.google.com " +
    "https://content.googleapis.com " +
    "https://login.microsoftonline.com " +
    "https://logincdn.msauth.net " +
    "https://logincdn.msftauth.net",

    // Child sources (for web workers and nested contexts)
    "child-src 'self' blob:",

    // Worker sources
    "worker-src 'self' blob:",

    // Media sources
    "media-src 'self' blob: data:",

    // Object sources - Restrict to none for security
    "object-src 'none'",

    // Base URI - Restrict to self
    "base-uri 'self'",

    // Form actions - Allow self and authentication providers
    "form-action 'self' " +
    "https://accounts.google.com " +
    "https://login.microsoftonline.com",

    // Frame ancestors - Prevent clickjacking
    "frame-ancestors 'none'",

    // Manifest source
    "manifest-src 'self'"
  ].join('; ');

  // Only apply CSP in development for testing - remove in production to avoid conflicts
  if (!isProduction() && !document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = csp;
    document.head.appendChild(meta);
    secureLog('CSP headers applied:', csp);
  }
};