// firebaseStorageService.ts - Handle Firebase Realtime Database operations

import { database } from '@/lib/firebase';
import { ref, push, set, get, query, orderByChild, equalTo, update } from 'firebase/database';
import { CheckResult } from './checkDefinitions';
import { AnalysisParameters } from './mainDocumentProcessor';
import { secureLog } from './securityUtils';

export interface StoredAnalysisResult {
  id: string;
  userId: string;
  companyName: string;
  timestamp: number;
  parameters: AnalysisParameters;
  results: Record<string, CheckResult>;
  documents: {
    audit_report?: { name: string; size: number; type: string };
    annexure_a?: { name: string; size: number; type: string };
    annexure_b?: { name: string; size: number; type: string };
    balance_sheet?: { name: string; size: number; type: string };
    notes?: { name: string; size: number; type: string };
    pl_notes?: { name: string; size: number; type: string };
    annual_report?: { name: string; size: number; type: string };
    secretarial_compliance?: { name: string; size: number; type: string };
    cfs?: { name: string; size: number; type: string }; // Consolidated Financial Statements document
  };
  summary: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  };
}

export interface Company {
  id: string;
  name: string;
  createdAt: number;
  userId: string;
}

export interface Partner {
  id: string;
  name: string;
  type: 'individual' | 'company' | 'subsidiary' | 'associate' | 'joint_venture';
  registrationNumber: string;
  createdAt: number;
  userId: string;
}

export interface FirmSettings {
  firmName: string;
  registrationNumber: string;
  address?: string;
  contactEmail?: string;
  phone?: string;
  preferences?: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    autoBackup: boolean;
    dataRetentionPeriod: number;
    defaultReportFormat: string;
    theme: string;
  };
  updatedAt: number;
  userId: string;
}

export async function saveAnalysisResults(
  userId: string,
  parameters: AnalysisParameters,
  results: Record<string, CheckResult>,
  documentFiles: Record<string, File>,
  summary: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  }
): Promise<string> {
  try {
    secureLog('🔍 Firebase: Saving analysis results for user:', { userId: '[REDACTED]' });

    // Prepare document metadata
    const documents: Record<string, { name: string; size: number; type: string }> = {};
    
    Object.entries(documentFiles).forEach(([key, file]) => {
      if (file) {
        documents[key] = {
          name: file.name,
          size: file.size,
          type: file.type
        };
      }
    });

    // Calculate summary
    const calculatedSummary = {
      total: summary.total,
      compliant: summary.compliant,
      nonCompliant: summary.nonCompliant,
      compliancePercentage: summary.compliancePercentage
    };

    // Create the analysis data
    const analysisData: Omit<StoredAnalysisResult, 'id'> = {
      userId,
      companyName: parameters.company_name,
      timestamp: Date.now(),
      parameters: {
        ...parameters,
        audit_date: parameters.audit_date.toISOString() // Convert Date to string for storage
      } as any,
      results,
      documents,
      summary: calculatedSummary
    };

    // Save to Firebase Realtime Database
    const analysisRef = ref(database, 'analyses');
    const newAnalysisRef = push(analysisRef);
    
    await set(newAnalysisRef, analysisData);
    
    const analysisId = newAnalysisRef.key!;
    secureLog('✅ Firebase: Analysis saved successfully with ID:', { analysisId });
    
    return analysisId;
  } catch (error) {
    secureLog('❌ Firebase: Error saving analysis results:', error);
    throw new Error(`Failed to save analysis results: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function getAnalysisHistory(userId: string): Promise<StoredAnalysisResult[]> {
  try {
    secureLog('🔍 Firebase: Fetching analysis history for user:', { userId: '[REDACTED]' });

    const analysesRef = ref(database, 'analyses');
    const userAnalysesQuery = query(analysesRef, orderByChild('userId'), equalTo(userId));
    const snapshot = await get(userAnalysesQuery);

    secureLog('🔍 Firebase: Snapshot exists:', { exists: snapshot.exists() });

    if (!snapshot.exists()) {
      secureLog('🔍 Firebase: No data found for user:', { userId: '[REDACTED]' });
      return [];
    }

    const analysesData = snapshot.val();
    const analyses: StoredAnalysisResult[] = [];

    // Convert the object to an array
    Object.keys(analysesData).forEach((key) => {
      const analysis = analysesData[key];
      analyses.push({
        id: key,
        userId: analysis.userId,
        companyName: analysis.companyName,
        timestamp: analysis.timestamp,
        parameters: {
          ...analysis.parameters,
          audit_date: new Date(analysis.parameters.audit_date) // Convert string back to Date
        },
        results: analysis.results,
        documents: analysis.documents,
        summary: analysis.summary
      });
    });

    // Sort by timestamp (descending - newest first)
    analyses.sort((a, b) => b.timestamp - a.timestamp);

    secureLog('✅ Firebase: Retrieved analysis history:', { count: analyses.length });
    return analyses;
  } catch (error) {
    secureLog('❌ Firebase: Error getting analysis history:', error);
    throw new Error(`Failed to get analysis history: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function getAnalysisById(analysisId: string, userId: string): Promise<StoredAnalysisResult | null> {
  try {
    secureLog('🔍 Firebase: Getting analysis by ID:', { analysisId, userId: '[REDACTED]' });

    const analysesRef = ref(database, 'analyses');
    const userAnalysesQuery = query(analysesRef, orderByChild('userId'), equalTo(userId));
    const snapshot = await get(userAnalysesQuery);

    if (!snapshot.exists()) {
      secureLog('🔍 Firebase: No analyses found for user:', { userId: '[REDACTED]' });
      return null;
    }

    const analysesData = snapshot.val();
    
    // Find the specific analysis
    for (const key in analysesData) {
      if (key === analysisId) {
        const analysis = analysesData[key];
        const result: StoredAnalysisResult = {
          id: key,
          userId: analysis.userId,
          companyName: analysis.companyName,
          timestamp: analysis.timestamp,
          parameters: {
            ...analysis.parameters,
            audit_date: new Date(analysis.parameters.audit_date)
          },
          results: analysis.results,
          documents: analysis.documents,
          summary: analysis.summary
        };

        secureLog('✅ Firebase: Retrieved analysis by ID:', { analysisId });
        return result;
      }
    }

    secureLog('🔍 Firebase: Analysis not found:', { analysisId });
    return null;
  } catch (error) {
    secureLog('❌ Firebase: Error getting analysis by ID:', error);
    throw new Error(`Failed to get analysis by ID: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function deleteAnalysis(analysisId: string, userId: string): Promise<void> {
  try {
    secureLog('🗑️ Firebase: Deleting analysis:', { analysisId, userId: '[REDACTED]' });

    const analysesRef = ref(database, 'analyses');
    const userAnalysesQuery = query(analysesRef, orderByChild('userId'), equalTo(userId));
    const snapshot = await get(userAnalysesQuery);

    if (!snapshot.exists()) {
      throw new Error('No analyses found for user');
    }

    const analysesData = snapshot.val();
    
    // Find and delete the specific analysis
    for (const key in analysesData) {
      if (key === analysisId) {
        const analysisRef = ref(database, `analyses/${key}`);
        await set(analysisRef, null);
        secureLog('✅ Firebase: Analysis deleted successfully:', { analysisId });
        return;
      }
    }

    throw new Error('Analysis not found');
  } catch (error) {
    secureLog('❌ Firebase: Error deleting analysis:', error);
    throw new Error(`Failed to delete analysis: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function getFirmSettings(userId: string): Promise<any> {
  try {
    secureLog('🔍 Firebase: Getting firm settings for user:', { userId: '[REDACTED]' });

    const firmSettingsRef = ref(database, `firm_settings/${userId}`);
    const snapshot = await get(firmSettingsRef);

    secureLog('🔍 Firebase: Snapshot exists:', { exists: snapshot.exists() });

    if (!snapshot.exists()) {
      secureLog('🔍 Firebase: No firm settings found for user:', { userId: '[REDACTED]' });
      return null;
    }

    const settings = snapshot.val();
    secureLog('✅ Firebase: Retrieved firm settings:', { hasSettings: !!settings });
    return settings;
  } catch (error) {
    secureLog('❌ Firebase: Error getting firm settings:', error);
    throw new Error(`Failed to get firm settings: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function updateFirmSettings(userId: string, settings: any): Promise<void> {
  try {
    secureLog('🔄 Firebase: Updating firm settings for user:', { userId: '[REDACTED]' });

    const firmSettingsRef = ref(database, `firm_settings/${userId}`);
    await set(firmSettingsRef, settings);

    secureLog('✅ Firebase: Firm settings updated successfully');
  } catch (error) {
    secureLog('❌ Firebase: Error updating firm settings:', error);
    throw new Error(`Failed to update firm settings: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function saveFirmSettings(
  userId: string, 
  firmName: string, 
  registrationNumber: string,
  address?: string,
  contactEmail?: string,
  phone?: string,
  preferences?: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    autoBackup: boolean;
    dataRetentionPeriod: number;
    defaultReportFormat: string;
    theme: string;
  }
): Promise<void> {
  try {
    secureLog('💾 Firebase: Saving firm settings for user:', { userId: '[REDACTED]' });
    
    const firmSettingsRef = ref(database, `firm_settings/${userId}`);
    
    const firmData: any = {
      firmName: firmName.trim(),
      registrationNumber: registrationNumber.trim(),
      updatedAt: Date.now(),
      userId
    };

    // Add optional fields if provided
    if (address) firmData.address = address.trim();
    if (contactEmail) firmData.contactEmail = contactEmail.trim();
    if (phone) firmData.phone = phone.trim();
    if (preferences) firmData.preferences = preferences;

    await set(firmSettingsRef, firmData);
    
    secureLog('✅ Firebase: Firm settings saved successfully');
  } catch (error) {
    secureLog('❌ Firebase: Error saving firm settings:', error);
    throw new Error(`Failed to save firm settings: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function getCompanies(userId: string): Promise<Company[]> {
  try {
    secureLog('🔍 Firebase: Getting companies for user:', { userId: '[REDACTED]' });

    const companiesRef = ref(database, `companies/${userId}`);
    const snapshot = await get(companiesRef);

    if (!snapshot.exists()) {
      secureLog('🔍 Firebase: No companies found for user:', { userId: '[REDACTED]' });
      return [];
    }

    const companiesData = snapshot.val();
    const companies: Company[] = [];

    Object.keys(companiesData).forEach((key) => {
      const company = companiesData[key];
      companies.push({
        id: key,
        name: company.name,
        createdAt: company.createdAt,
        userId: company.userId
      });
    });

    // Sort by createdAt (descending - newest first)
    companies.sort((a, b) => b.createdAt - a.createdAt);

    secureLog('✅ Firebase: Retrieved companies:', { count: companies.length });
    return companies;
  } catch (error) {
    secureLog('❌ Firebase: Error getting companies:', error);
    throw new Error(`Failed to get companies: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function addCompany(userId: string, name: string): Promise<string> {
  try {
    secureLog('➕ Firebase: Adding company for user:', { userId: '[REDACTED]', name });

    const userCompaniesRef = ref(database, `companies/${userId}`);
    const newCompanyRef = push(userCompaniesRef);
    
    const companyData: Omit<Company, 'id'> = {
      name: name.trim(),
      createdAt: Date.now(),
      userId
    };

    await set(newCompanyRef, companyData);
    
    const companyId = newCompanyRef.key!;
    secureLog('✅ Firebase: Company added successfully:', { companyId });
    
    return companyId;
  } catch (error) {
    secureLog('❌ Firebase: Error adding company:', error);
    throw new Error(`Failed to add company: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function updateCompany(userId: string, companyId: string, newName: string): Promise<void> {
  try {
    secureLog('🔄 Firebase: Updating company:', { companyId, userId: '[REDACTED]', newName });

    const companyRef = ref(database, `companies/${userId}/${companyId}`);
    await update(companyRef, { name: newName.trim() });

    secureLog('✅ Firebase: Company updated successfully:', { companyId });
  } catch (error) {
    secureLog('❌ Firebase: Error updating company:', error);
    throw new Error(`Failed to update company: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function deleteCompany(userId: string, companyId: string): Promise<void> {
  try {
    secureLog('🗑️ Firebase: Deleting company:', { companyId, userId: '[REDACTED]' });

    const companyRef = ref(database, `companies/${userId}/${companyId}`);
    await set(companyRef, null);

    secureLog('✅ Firebase: Company deleted successfully:', { companyId });
  } catch (error) {
    secureLog('❌ Firebase: Error deleting company:', error);
    throw new Error(`Failed to delete company: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function getPartners(userId: string): Promise<Partner[]> {
  try {
    secureLog('🔍 Firebase: Getting partners for user:', { userId: '[REDACTED]' });

    const partnersRef = ref(database, `partners/${userId}`);
    const snapshot = await get(partnersRef);

    if (!snapshot.exists()) {
      secureLog('🔍 Firebase: No partners found for user:', { userId: '[REDACTED]' });
      return [];
    }

    const partnersData = snapshot.val();
    const partners: Partner[] = [];

    Object.keys(partnersData).forEach((key) => {
      const partner = partnersData[key];
      partners.push({
        id: key,
        name: partner.name,
        type: partner.type,
        registrationNumber: partner.registrationNumber,
        createdAt: partner.createdAt,
        userId: partner.userId
      });
    });

    // Sort by createdAt (descending - newest first)
    partners.sort((a, b) => b.createdAt - a.createdAt);

    secureLog('✅ Firebase: Retrieved partners:', { count: partners.length });
    return partners;
  } catch (error) {
    secureLog('❌ Firebase: Error getting partners:', error);
    throw new Error(`Failed to get partners: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function addPartner(userId: string, name: string, registrationNumber: string, type: 'individual' | 'company' | 'subsidiary' | 'associate' | 'joint_venture' = 'company'): Promise<string> {
  try {
    secureLog('➕ Firebase: Adding partner for user:', { userId: '[REDACTED]', name, type });

    const userPartnersRef = ref(database, `partners/${userId}`);
    const newPartnerRef = push(userPartnersRef);
    
    const partnerData: Omit<Partner, 'id'> = {
      name: name.trim(),
      type: type,
      registrationNumber: registrationNumber.trim(),
      createdAt: Date.now(),
      userId
    };

    await set(newPartnerRef, partnerData);
    
    const partnerId = newPartnerRef.key!;
    secureLog('✅ Firebase: Partner added successfully:', { partnerId });
    
    return partnerId;
  } catch (error) {
    secureLog('❌ Firebase: Error adding partner:', error);
    throw new Error(`Failed to add partner: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function updatePartner(userId: string, partnerId: string, newName: string, newRegistrationNumber: string): Promise<void> {
  try {
    secureLog('🔄 Firebase: Updating partner:', { partnerId, userId: '[REDACTED]', newName });

    const partnerRef = ref(database, `partners/${userId}/${partnerId}`);
    await update(partnerRef, { 
      name: newName.trim(),
      registrationNumber: newRegistrationNumber.trim()
    });

    secureLog('✅ Firebase: Partner updated successfully:', { partnerId });
  } catch (error) {
    secureLog('❌ Firebase: Error updating partner:', error);
    throw new Error(`Failed to update partner: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function deletePartner(userId: string, partnerId: string): Promise<void> {
  try {
    secureLog('🗑️ Firebase: Deleting partner:', { partnerId, userId: '[REDACTED]' });

    const partnerRef = ref(database, `partners/${userId}/${partnerId}`);
    await set(partnerRef, null);

    secureLog('✅ Firebase: Partner deleted successfully:', { partnerId });
  } catch (error) {
    secureLog('❌ Firebase: Error deleting partner:', error);
    throw new Error(`Failed to delete partner: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function deleteFirmSettings(userId: string): Promise<void> {
  try {
    secureLog('🗑️ Firebase: Deleting firm settings for user:', { userId: '[REDACTED]' });
    
    const firmSettingsRef = ref(database, `firm_settings/${userId}`);
    
    // Verify the settings exist
    const snapshot = await get(firmSettingsRef);
    if (!snapshot.exists()) {
      throw new Error('Firm settings not found');
    }

    await set(firmSettingsRef, null);
    
    secureLog('✅ Firebase: Firm settings deleted successfully');
  } catch (error) {
    secureLog('❌ Firebase: Error deleting firm settings:', error);
    throw new Error(`Failed to delete firm settings: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Helper function to get company names for dropdowns
 */
export async function getCompanyNames(userId: string): Promise<string[]> {
  try {
    const companies = await getCompanies(userId);
    return companies.map(company => company.name);
  } catch (error) {
    secureLog('❌ Firebase: Error fetching company names:', error);
    return [];
  }
}