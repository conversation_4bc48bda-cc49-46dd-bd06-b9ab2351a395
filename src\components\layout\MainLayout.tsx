import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import Header from "./Header";
import DashboardSidebar from "./DashboardSidebar";
import LoadingScreen from "@/components/common/LoadingScreen";
import { secureLog } from "@/lib/securityUtils";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { currentUser, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  
  // Check if this is the landing page or auth pages that need full-width layout
  const isLandingPage = location.pathname === '/';
  const isAuthPage = ['/login', '/register', '/forgot-password'].includes(location.pathname);
  const isContentPage = ['/blog', '/resources', '/case-studies', '/terms', '/privacy', '/cookies', '/security', '/compliance', '/gdpr'].includes(location.pathname);
  const isDashboardPage = location.pathname.startsWith('/dashboard');
  
  // Handle loading state
  useEffect(() => {
    // Show loading state when changing routes
    setLoading(true);
    
    // Hide loading after a short delay to simulate content loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 300); // Reduced from 500ms to 300ms for better UX
    
    return () => clearTimeout(timer);
  }, [location.pathname]);

  // If user is logged in and at the root path, redirect them to the dashboard
  useEffect(() => {
    if (currentUser && !authLoading && location.pathname === '/') {
      secureLog("User is logged in and at root path, redirecting to dashboard");
      navigate('/dashboard', { replace: true });
    }
  }, [currentUser, authLoading, location, navigate]);

  // Show loading screen during authentication check
  if (authLoading) {
    return <LoadingScreen />;
  }

  // Show loading screen during route transitions
  if (loading) {
    return <LoadingScreen />;
  }

  // Landing page - Full width, no containers
  if (isLandingPage) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }
  
  // Auth pages (Login, Register, Forgot Password) - Full width with dark background
  if (isAuthPage) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }
  
  // Content pages - Full width with dark background
  if (isContentPage) {
    return (
      <div className="min-h-screen w-full bg-slate-900">
        {children}
      </div>
    );
  }

  // Dashboard pages - Layout with sidebar and header
  if (isDashboardPage && currentUser) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex min-h-[calc(100vh-64px)]">
          {/* Sidebar placeholder for spacing */}
          <div id="sidebar-placeholder" className="w-64 flex-shrink-0 transition-all duration-300" />

          <main className="flex-1 p-4 md:p-6 overflow-auto">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>

          {/* Actual Sidebar - Fixed Position */}
          <DashboardSidebar />
        </div>
      </div>
    );
  }

  // Redirect unauthenticated users trying to access dashboard
  if (isDashboardPage && !currentUser && !authLoading) {
    useEffect(() => {
      navigate('/login', { replace: true });
    }, [navigate]);
    
    return <LoadingScreen />;
  }

  // Default layout for other pages
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </div>
      </main>
    </div>
  );
};

export default MainLayout;