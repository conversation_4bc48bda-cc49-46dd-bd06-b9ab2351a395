// firebase.ts
import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getStorage, ref as storageRef, getMetadata } from "firebase/storage";
import { getDatabase } from "firebase/database";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
};

// Validate Firebase configuration
const validateFirebaseConfig = () => {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'appId'];
  const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig]);

  if (missingFields.length > 0) {
    console.error('Missing Firebase configuration fields:', missingFields);
    throw new Error(`Firebase configuration incomplete. Missing: ${missingFields.join(', ')}`);
  }
};

// Validate configuration before initializing
validateFirebaseConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// App Check has been removed for compatibility

// Initialize Firebase services with error handling
let auth, storage, database;

try {
  auth = getAuth(app);
  storage = getStorage(app);
  database = getDatabase(app);

  // Only connect to emulator in development
  if (import.meta.env.DEV && !auth.config.emulator) {
    // This is just for development - remove in production
    console.log('Firebase services initialized successfully');
  }
} catch (error) {
  console.error('Failed to initialize Firebase services:', error);
  throw new Error('Firebase initialization failed. Please check your configuration.');
}

// Create a cache for downloaded URLs to avoid repeated Firebase Storage calls
export const storageCache = new Map<string, {
  url: string;
  timestamp: number;
  size: number;
}>();

// Maximum cache size (100MB)
const MAX_CACHE_SIZE = 100 * 1024 * 1024;
let currentCacheSize = 0;

// Cache expiration time (30 minutes)
const CACHE_EXPIRATION = 30 * 60 * 1000;

/**
 * Add a URL to the storage cache
 */
export const addToStorageCache = (path: string, url: string, size: number = 0) => {
  // If adding this would exceed the cache size, clear old entries
  if (currentCacheSize + size > MAX_CACHE_SIZE) {
    // Sort by timestamp (oldest first)
    const entries = Array.from(storageCache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove oldest entries until we have enough space
    while (currentCacheSize + size > MAX_CACHE_SIZE && entries.length > 0) {
      const [oldestKey, oldestValue] = entries.shift()!;
      storageCache.delete(oldestKey);
      currentCacheSize -= oldestValue.size;
      console.log(`Removed ${oldestKey} from storage cache to free up space`);
    }
  }

  // Add to cache
  storageCache.set(path, {
    url,
    timestamp: Date.now(),
    size
  });

  currentCacheSize += size;
  console.log(`Added ${path} to storage cache (${size} bytes)`);
};

/**
 * Get a URL from the storage cache if it exists and is not expired
 */
export const getFromStorageCache = (path: string): string | null => {
  const cached = storageCache.get(path);

  if (!cached) {
    return null;
  }

  // Check if expired
  if (Date.now() - cached.timestamp > CACHE_EXPIRATION) {
    storageCache.delete(path);
    currentCacheSize -= cached.size;
    console.log(`Cache entry for ${path} expired and was removed`);
    return null;
  }

  console.log(`Cache hit for ${path}`);
  return cached.url;
};

export { app, auth, storage, database };