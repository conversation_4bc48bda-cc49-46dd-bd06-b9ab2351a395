<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #results {
            margin-top: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Firebase Connection Test</h1>
        <p>This page tests Firebase connectivity without any CSP restrictions.</p>
        
        <button id="testBtn" onclick="runFirebaseTest()">Run Firebase Test</button>
        
        <div id="results"></div>
    </div>

    <script type="module">
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC2tY3W2bet6tfCVlU_V77gYiP9jXf6nzY",
            authDomain: "checklistaicap.firebaseapp.com",
            databaseURL: "https://checklistaicap-default-rtdb.firebaseio.com",
            projectId: "checklistaicap",
            storageBucket: "checklistaicap.firebasestorage.app",
            messagingSenderId: "924373606572",
            appId: "1:924373606572:web:87684cb58403bc7a723f09"
        };

        let testResults = [];

        function addResult(test, status, message, details = null) {
            testResults.push({ test, status, message, details });
            updateResults();
        }

        function updateResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = testResults.map(result => {
                const statusClass = result.status === 'success' ? 'success' : 
                                  result.status === 'error' ? 'error' : 'info';
                return `
                    <div class="test-result ${statusClass}">
                        <strong>${result.test}:</strong> ${result.message}
                        ${result.details ? `<pre>${JSON.stringify(result.details, null, 2)}</pre>` : ''}
                    </div>
                `;
            }).join('');
        }

        window.runFirebaseTest = async function() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = 'Running Tests...';
            testResults = [];

            try {
                // Test 1: Check if Firebase SDK can be loaded
                addResult('Firebase SDK Loading', 'info', 'Attempting to load Firebase SDK...');
                
                const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                const { getDatabase, ref, get } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-database.js');
                
                addResult('Firebase SDK Loading', 'success', 'Firebase SDK loaded successfully');

                // Test 2: Initialize Firebase
                addResult('Firebase Initialization', 'info', 'Initializing Firebase app...');
                const app = initializeApp(firebaseConfig);
                addResult('Firebase Initialization', 'success', 'Firebase app initialized successfully');

                // Test 3: Database connection
                addResult('Database Connection', 'info', 'Testing database connection...');
                const database = getDatabase(app);
                const connectedRef = ref(database, '.info/connected');
                
                try {
                    const snapshot = await get(connectedRef);
                    if (snapshot.val() === true) {
                        addResult('Database Connection', 'success', 'Database connection established');
                    } else {
                        addResult('Database Connection', 'error', 'Database connection failed');
                    }
                } catch (dbError) {
                    addResult('Database Connection', 'error', `Database connection error: ${dbError.message}`, dbError);
                }

                // Test 4: Check CSP violations
                addResult('CSP Check', 'info', 'Checking for CSP violations...');
                
                // Listen for CSP violations
                let cspViolations = [];
                const originalConsoleError = console.error;
                console.error = function(...args) {
                    const message = args.join(' ');
                    if (message.includes('Content Security Policy') || message.includes('CSP')) {
                        cspViolations.push(message);
                    }
                    originalConsoleError.apply(console, args);
                };

                // Wait a bit to catch any CSP violations
                setTimeout(() => {
                    if (cspViolations.length === 0) {
                        addResult('CSP Check', 'success', 'No CSP violations detected');
                    } else {
                        addResult('CSP Check', 'error', `${cspViolations.length} CSP violations detected`, cspViolations);
                    }
                    console.error = originalConsoleError;
                }, 2000);

            } catch (error) {
                addResult('Firebase Test', 'error', `Test failed: ${error.message}`, error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = 'Run Firebase Test';
            }
        };

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(runFirebaseTest, 1000);
        });
    </script>
</body>
</html>
