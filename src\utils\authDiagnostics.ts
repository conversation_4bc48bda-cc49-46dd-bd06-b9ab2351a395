// Authentication Diagnostics Tool
import { auth } from '@/lib/firebase';
import { OAuthProvider } from 'firebase/auth';

export interface DiagnosticResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

export class AuthDiagnostics {
  private results: DiagnosticResult[] = [];

  private addResult(category: string, test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ category, test, status, message, details });
  }

  async runAllDiagnostics(): Promise<DiagnosticResult[]> {
    this.results = [];

    console.log('🔍 Running Authentication Diagnostics...');

    await this.checkEnvironmentVariables();
    await this.checkFirebaseConfiguration();
    await this.checkBrowserCompatibility();
    await this.checkNetworkConnectivity();
    await this.checkSecurityHeaders();
    await this.checkMicrosoftProvider();

    return this.results;
  }

  private async checkEnvironmentVariables() {
    const category = 'Environment Variables';

    const requiredVars = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_AUTH_DOMAIN',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_APP_ID',
      'VITE_AZURE_TENANT_ID'
    ];

    for (const varName of requiredVars) {
      const value = import.meta.env[varName];
      if (!value) {
        this.addResult(category, varName, 'FAIL', `Missing environment variable: ${varName}`);
      } else {
        this.addResult(category, varName, 'PASS', `Environment variable present`);
      }
    }
  }

  private async checkFirebaseConfiguration() {
    const category = 'Firebase Configuration';

    try {
      // Check if Firebase auth is initialized
      if (auth) {
        this.addResult(category, 'Firebase Auth', 'PASS', 'Firebase Auth initialized successfully');

        // Check auth domain
        const authDomain = import.meta.env.VITE_FIREBASE_AUTH_DOMAIN;
        if (authDomain && authDomain.includes('firebaseapp.com')) {
          this.addResult(category, 'Auth Domain', 'PASS', `Auth domain: ${authDomain}`);
        } else {
          this.addResult(category, 'Auth Domain', 'WARNING', `Unusual auth domain: ${authDomain}`);
        }
      } else {
        this.addResult(category, 'Firebase Auth', 'FAIL', 'Firebase Auth not initialized');
      }
    } catch (error) {
      this.addResult(category, 'Firebase Auth', 'FAIL', `Firebase initialization error: ${error}`);
    }
  }

  private async checkBrowserCompatibility() {
    const category = 'Browser Compatibility';

    // Check if running in secure context (HTTPS or localhost)
    if (window.isSecureContext) {
      this.addResult(category, 'Secure Context', 'PASS', 'Running in secure context');
    } else {
      this.addResult(category, 'Secure Context', 'FAIL', 'Not running in secure context (HTTPS required)');
    }

    // Check popup support
    try {
      const popup = window.open('', '_blank', 'width=1,height=1');
      if (popup) {
        popup.close();
        this.addResult(category, 'Popup Support', 'PASS', 'Browser allows popups');
      } else {
        this.addResult(category, 'Popup Support', 'FAIL', 'Browser blocks popups');
      }
    } catch (error) {
      this.addResult(category, 'Popup Support', 'FAIL', `Popup test failed: ${error}`);
    }

    // Check localStorage
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      this.addResult(category, 'Local Storage', 'PASS', 'Local storage available');
    } catch (error) {
      this.addResult(category, 'Local Storage', 'FAIL', `Local storage not available: ${error}`);
    }
  }

  private async checkNetworkConnectivity() {
    const category = 'Network Connectivity';

    const endpoints = [
      'https://identitytoolkit.googleapis.com',
      'https://securetoken.googleapis.com',
      'https://accounts.google.com',
      'https://login.microsoftonline.com'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'HEAD',
          mode: 'no-cors',
          cache: 'no-cache'
        });
        this.addResult(category, `Connectivity: ${endpoint}`, 'PASS', 'Endpoint reachable');
      } catch (error) {
        this.addResult(category, `Connectivity: ${endpoint}`, 'WARNING', `May not be reachable: ${error}`);
      }
    }
  }

  private async checkSecurityHeaders() {
    const category = 'Security Headers';

    try {
      const response = await fetch(window.location.href, { method: 'HEAD' });
      const headers = response.headers;

      // Check CSP
      const csp = headers.get('content-security-policy');
      if (csp) {
        const hasGoogleApis = csp.includes('googleapis.com');
        const hasMicrosoft = csp.includes('login.microsoftonline.com') || csp.includes('accounts.google.com');

        if (hasGoogleApis && hasMicrosoft) {
          this.addResult(category, 'CSP', 'PASS', 'CSP allows required authentication domains');
        } else {
          this.addResult(category, 'CSP', 'WARNING', 'CSP may be blocking authentication domains', { csp });
        }
      } else {
        this.addResult(category, 'CSP', 'WARNING', 'No CSP header found');
      }

      // Check X-Frame-Options
      const xFrameOptions = headers.get('x-frame-options');
      if (xFrameOptions === 'DENY') {
        this.addResult(category, 'X-Frame-Options', 'WARNING', 'X-Frame-Options: DENY may block OAuth popups');
      } else {
        this.addResult(category, 'X-Frame-Options', 'PASS', `X-Frame-Options: ${xFrameOptions || 'not set'}`);
      }

    } catch (error) {
      this.addResult(category, 'Security Headers', 'WARNING', `Could not check headers: ${error}`);
    }
  }

  private async checkMicrosoftProvider() {
    const category = 'Microsoft OAuth Provider';

    try {
      const provider = new OAuthProvider('microsoft.com');
      const tenantId = import.meta.env.VITE_AZURE_TENANT_ID;

      if (tenantId) {
        provider.setCustomParameters({
          tenant: tenantId,
          prompt: 'select_account',
          domain_hint: 'organizations'
        });
        this.addResult(category, 'Provider Configuration', 'PASS', `Microsoft provider configured with tenant: ${tenantId}`);
      } else {
        this.addResult(category, 'Provider Configuration', 'FAIL', 'Missing Azure tenant ID');
      }

      // Check scopes
      provider.addScope('email');
      provider.addScope('profile');
      provider.addScope('openid');

      this.addResult(category, 'OAuth Scopes', 'PASS', 'Required scopes configured');

    } catch (error) {
      this.addResult(category, 'Provider Configuration', 'FAIL', `Provider setup failed: ${error}`);
    }
  }

  printResults() {
    console.log('\n🔍 Authentication Diagnostics Results:');
    console.log('=====================================');

    const categories = [...new Set(this.results.map(r => r.category))];

    for (const category of categories) {
      console.log(`\n📋 ${category}:`);
      const categoryResults = this.results.filter(r => r.category === category);

      for (const result of categoryResults) {
        const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        console.log(`  ${icon} ${result.test}: ${result.message}`);
        if (result.details) {
          console.log(`     Details:`, result.details);
        }
      }
    }

    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const warningCount = this.results.filter(r => r.status === 'WARNING').length;

    console.log('\n📊 Summary:');
    console.log(`   ✅ Passed: ${this.results.filter(r => r.status === 'PASS').length}`);
    console.log(`   ⚠️  Warnings: ${warningCount}`);
    console.log(`   ❌ Failed: ${failCount}`);

    if (failCount > 0) {
      console.log('\n🚨 Critical issues found that need to be fixed!');
    } else if (warningCount > 0) {
      console.log('\n⚠️  Some warnings found - authentication may still work but could be improved.');
    } else {
      console.log('\n🎉 All diagnostics passed!');
    }
  }
}

// Export function to run diagnostics
export async function runAuthDiagnostics(): Promise<DiagnosticResult[]> {
  const diagnostics = new AuthDiagnostics();
  const results = await diagnostics.runAllDiagnostics();
  diagnostics.printResults();
  return results;
}

export default runAuthDiagnostics;
