// History.tsx - Updated to work with Firebase Realtime Database

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery } from "@tanstack/react-query";
import { getAnalysisHistory, StoredAnalysisResult } from "@/lib/firebaseStorageService";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { FileText, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { secureLog } from "@/lib/securityUtils";

const History = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [error, setError] = useState<string | null>(null);

  const {
    data: analysisHistory,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ["analysisHistory", currentUser?.uid],
    queryFn: async () => {
      if (!currentUser) return [];

      try {
        secureLog("Fetching analysis history for user:", { userId: '[REDACTED]' });
        const history = await getAnalysisHistory(currentUser.uid);
        secureLog("Fetched history:", { count: history.length });
        return history;
      } catch (err) {
        secureLog("Error fetching analysis history:", err);
        throw new Error(`Error fetching analysis history: ${err instanceof Error ? err.message : String(err)}`);
      }
    },
    enabled: !!currentUser,
    retry: 2,
    staleTime: 30000, // 30 seconds
  });

  useEffect(() => {
    if (queryError) {
      secureLog("Query error:", queryError);
      setError(`Error loading history: ${queryError instanceof Error ? queryError.message : String(queryError)}`);
    }
  }, [queryError]);

  const handleViewDetails = (id: string) => {
    secureLog("Navigating to analysis details:", { id });
    navigate(`/dashboard/analysis/${id}`);
  };

  const getComplianceColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getComplianceBadgeColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-100 text-green-800";
    if (percentage >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const refreshHistory = () => {
    refetch();
    toast({
      title: "Refreshing",
      description: "Analysis history is being refreshed...",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Analysis History</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={refreshHistory}>
            Refresh
          </Button>
          <Button onClick={() => navigate("/dashboard/analyzer")}>
            New Analysis
          </Button>
        </div>
      </div>

      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      ) : !analysisHistory || analysisHistory.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">No analysis history</h3>
          <p className="text-gray-500 mb-6">
            You haven't performed any analysis yet. Start by analyzing your documents.
          </p>
          <Button onClick={() => navigate("/dashboard/analyzer")}>
            Start New Analysis
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {analysisHistory.map((analysis: StoredAnalysisResult) => (
            <div
              key={analysis.id}
              className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleViewDetails(analysis.id)}
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold mb-2">
                    {analysis.companyName}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    Analyzed on {format(new Date(analysis.timestamp), "MMM d, yyyy 'at' h:mm a")}
                  </p>
                  <div className="mt-2 text-sm text-gray-600">
                    <span className="inline-block mr-4">
                      Report Type: {analysis.parameters?.audit_report_type || "Standard"}
                    </span>
                    {analysis.parameters?.is_nbfc === "Yes" && (
                      <span className="inline-block mr-4 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        NBFC
                      </span>
                    )}
                    {analysis.parameters?.company_listing_status === "Listed" && (
                      <span className="inline-block mr-4 bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
                        Listed
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-3xl font-bold ${getComplianceColor(analysis.summary?.compliancePercentage || 0)}`}>
                    {analysis.summary?.compliancePercentage || 0}%
                  </div>
                  <div className="text-sm text-gray-500">Compliance</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex space-x-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {analysis.summary?.total || 0}
                    </div>
                    <div className="text-xs text-gray-500">Total Checks</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {analysis.summary?.compliant || 0}
                    </div>
                    <div className="text-xs text-gray-500">Passed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {analysis.summary?.nonCompliant || 0}
                    </div>
                    <div className="text-xs text-gray-500">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">
                      {analysis.documents ? Object.keys(analysis.documents).length : 0}
                    </div>
                    <div className="text-xs text-gray-500">Documents</div>
                  </div>
                </div>

                <div className={`px-3 py-1 rounded-full text-sm font-medium ${getComplianceBadgeColor(analysis.summary?.compliancePercentage || 0)}`}>
                  {analysis.summary?.compliancePercentage || 0}% Compliant
                </div>
              </div>

              {/* Progress bar */}
              <div className="mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      (analysis.summary?.compliancePercentage || 0) >= 80
                        ? "bg-green-500"
                        : (analysis.summary?.compliancePercentage || 0) >= 60
                        ? "bg-yellow-500"
                        : "bg-red-500"
                    }`}
                    style={{ width: `${analysis.summary?.compliancePercentage || 0}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default History;
