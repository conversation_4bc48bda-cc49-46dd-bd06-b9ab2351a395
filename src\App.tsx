
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Routes, Route, BrowserRouter, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { storageCache } from "@/lib/firebase";
import { secureLog, initializeSecurity, setCSPHeaders } from "@/lib/securityUtils";
import { runAuthDiagnostics } from "@/utils/authDiagnostics";

// Initialize global storage cache
declare global {
  interface Window {
    storageCache: Map<string, {
      url: string;
      timestamp: number;
      size: number;
    }>;
  }
}

// Pages
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import Analyzer from "./pages/dashboard/Analyzer";
import History from "./pages/dashboard/History";
import Checklists from "./pages/dashboard/Checklists";
import Settings from "./pages/dashboard/Settings";
import AnalysisDetail from "./pages/dashboard/AnalysisDetail";
import Documents from "./pages/dashboard/Documents";
import DocumentDiagnostics from "./pages/document-diagnostics";
// Content pages
import Blog from "./pages/blog";
import Resources from "./pages/resources";
import CaseStudies from "./pages/case-studies";
import Terms from "./pages/terms";
import Privacy from "./pages/privacy";
import Cookies from "./pages/cookies";
import Security from "./pages/security";
import Compliance from "./pages/compliance";
import GDPR from "./pages/gdpr";
// PDF Analysis functionality has been integrated into the Analyzer component
import NotFound from "./pages/NotFound";

// Layouts
import MainLayout from "./components/layout/MainLayout";
import AuthLayout from "./components/layout/AuthLayout";
import DashboardLayout from "./components/layout/DashboardLayout";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const PrivateRoute = ({ children }: { children: React.ReactNode }) => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  return <>{children}</>;
};

const AppRoutes = () => {  return (
    <BrowserRouter>
      <Routes>
        {/* Public Routes with MainLayout */}
        <Route path="/" element={
          <MainLayout>
            <Index />
          </MainLayout>
        } />

        {/* Auth Routes with AuthLayout */}
        <Route path="/login" element={
          <AuthLayout>
            <Login />
          </AuthLayout>
        } />
        <Route path="/register" element={
          <AuthLayout>
            <Register />
          </AuthLayout>
        } />

        {/* Protected Routes with DashboardLayout */}
        <Route path="/dashboard" element={
          <PrivateRoute>
            <DashboardLayout>
              <Dashboard />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/documents" element={
          <PrivateRoute>
            <DashboardLayout>
              <Documents />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/analyzer" element={
          <PrivateRoute>
            <DashboardLayout>
              <Analyzer />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/history" element={
          <PrivateRoute>
            <DashboardLayout>
              <History />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/checklists" element={
          <PrivateRoute>
            <DashboardLayout>
              <Checklists />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/settings" element={
          <PrivateRoute>
            <DashboardLayout>
              <Settings />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/document-diagnostics" element={
          <PrivateRoute>
            <DashboardLayout>
              <DocumentDiagnostics />
            </DashboardLayout>
          </PrivateRoute>
        } />        {/* Keep old route for backward compatibility */}
        <Route path="/dashboard/test-pdf" element={
          <PrivateRoute>
            <DashboardLayout>
              <Navigate to="/dashboard/document-diagnostics" replace />
            </DashboardLayout>
          </PrivateRoute>
        } />
        {/* PDF Analysis functionality has been integrated into the existing Analysis tab */}
        <Route path="/dashboard/pdf-analysis" element={
          <PrivateRoute>
            <DashboardLayout>
              <Navigate to="/dashboard/analyzer" replace />
            </DashboardLayout>
          </PrivateRoute>
        } />
        <Route path="/dashboard/analysis/:id" element={
          <PrivateRoute>
            <DashboardLayout>
              <AnalysisDetail />
            </DashboardLayout>
          </PrivateRoute>
        } />        {/* Content pages */}
        <Route path="/blog" element={
          <MainLayout>
            <Blog />
          </MainLayout>
        } />
        <Route path="/resources" element={
          <MainLayout>
            <Resources />
          </MainLayout>
        } />
        <Route path="/case-studies" element={
          <MainLayout>
            <CaseStudies />
          </MainLayout>
        } />
        <Route path="/terms" element={
          <MainLayout>
            <Terms />
          </MainLayout>
        } />
        <Route path="/privacy" element={
          <MainLayout>
            <Privacy />
          </MainLayout>
        } />        <Route path="/cookies" element={
          <MainLayout>
            <Cookies />
          </MainLayout>
        } />
        <Route path="/security" element={
          <MainLayout>
            <Security />
          </MainLayout>
        } />
        <Route path="/compliance" element={
          <MainLayout>
            <Compliance />
          </MainLayout>
        } />
        <Route path="/gdpr" element={
          <MainLayout>
            <GDPR />
          </MainLayout>
        } />

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
};

const App = () => {
  // Initialize the global storage cache and security
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize security checks gracefully
      try {
        // Only initialize security in production to avoid Firebase blocking in development
        if (import.meta.env.PROD) {
          initializeSecurity();
          setCSPHeaders();
        } else {
          // In development, just log that security is disabled
          console.log('🔧 Development mode: Security restrictions disabled for Firebase compatibility');
        }
        secureLog('Security initialization completed successfully');
      } catch (error) {
        console.warn('Security initialization warning:', error);
        // Don't block the app, just log warnings
      }

      window.storageCache = storageCache;
      // Make diagnostics available globally for debugging
      (window as any).runAuthDiagnostics = runAuthDiagnostics;
      secureLog('Global storage cache initialized');
      secureLog('Auth diagnostics available: window.runAuthDiagnostics()');
    }

    // Clean up expired cache entries every 5 minutes
    const cacheCleanupInterval = setInterval(() => {
      if (typeof window !== 'undefined' && window.storageCache) {
        const now = Date.now();
        const CACHE_EXPIRATION = 30 * 60 * 1000; // 30 minutes
        let expiredCount = 0;

        // Check each entry for expiration
        window.storageCache.forEach((value, key) => {
          if (now - value.timestamp > CACHE_EXPIRATION) {
            window.storageCache.delete(key);
            expiredCount++;
          }
        });

        if (expiredCount > 0) {
          secureLog(`Cleaned up ${expiredCount} expired cache entries`);
        }
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(cacheCleanupInterval);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <AppRoutes />
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
