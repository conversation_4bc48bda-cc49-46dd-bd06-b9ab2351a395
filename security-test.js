// Security Testing Script
// Run this script to validate security configurations

const https = require('https');
const http = require('http');

const DOMAIN = 'checklistaicap.firebaseapp.com'; // Replace with your actual domain

// Test security headers
async function testSecurityHeaders() {
  console.log('🔒 Testing Security Headers...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: DOMAIN,
      port: 443,
      path: '/',
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      console.log('\n📋 Response Headers:');
      
      const securityHeaders = {
        'x-frame-options': 'X-Frame-Options',
        'x-content-type-options': 'X-Content-Type-Options',
        'x-xss-protection': 'X-XSS-Protection',
        'strict-transport-security': 'Strict-Transport-Security',
        'content-security-policy': 'Content-Security-Policy',
        'referrer-policy': 'Referrer-Policy',
        'permissions-policy': 'Permissions-Policy'
      };

      let passed = 0;
      let total = Object.keys(securityHeaders).length;

      Object.keys(securityHeaders).forEach(header => {
        if (res.headers[header]) {
          console.log(`✅ ${securityHeaders[header]}: ${res.headers[header]}`);
          passed++;
        } else {
          console.log(`❌ ${securityHeaders[header]}: Missing`);
        }
      });

      console.log(`\n📊 Security Headers Score: ${passed}/${total}`);
      resolve({ passed, total });
    });

    req.on('error', (e) => {
      console.error('❌ Error testing security headers:', e.message);
      reject(e);
    });

    req.end();
  });
}

// Test CORS configuration
async function testCORS() {
  console.log('\n🌐 Testing CORS Configuration...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: DOMAIN,
      port: 443,
      path: '/',
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://malicious-site.com',
        'Access-Control-Request-Method': 'GET'
      }
    };

    const req = https.request(options, (res) => {
      const corsHeaders = res.headers['access-control-allow-origin'];
      
      if (corsHeaders === '*') {
        console.log('❌ CORS allows all origins (*)');
        resolve({ secure: false, message: 'Wildcard origin detected' });
      } else if (corsHeaders && corsHeaders.includes('malicious-site.com')) {
        console.log('❌ CORS allows malicious origin');
        resolve({ secure: false, message: 'Malicious origin allowed' });
      } else {
        console.log('✅ CORS configuration appears secure');
        console.log(`   Allowed origins: ${corsHeaders || 'None'}`);
        resolve({ secure: true, message: 'CORS properly configured' });
      }
    });

    req.on('error', (e) => {
      console.log('✅ OPTIONS method blocked or CORS properly configured');
      resolve({ secure: true, message: 'OPTIONS method blocked' });
    });

    req.end();
  });
}

// Test Firebase config exposure
async function testFirebaseConfig() {
  console.log('\n🔥 Testing Firebase Configuration...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: DOMAIN,
      port: 443,
      path: '/__/firebase/init.json',
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const config = JSON.parse(data);
          
          console.log('ℹ️  Firebase config is accessible (this is expected)');
          console.log('   Cache-Control:', res.headers['cache-control']);
          console.log('   X-Robots-Tag:', res.headers['x-robots-tag']);
          
          // Check if sensitive data is exposed
          const sensitiveKeys = ['privateKey', 'clientSecret', 'databasePassword'];
          const hasSensitiveData = sensitiveKeys.some(key => 
            JSON.stringify(config).toLowerCase().includes(key.toLowerCase())
          );
          
          if (hasSensitiveData) {
            console.log('❌ Sensitive data detected in Firebase config');
            resolve({ secure: false, message: 'Sensitive data exposed' });
          } else {
            console.log('✅ No sensitive data in Firebase config');
            resolve({ secure: true, message: 'Config properly secured' });
          }
        } catch (e) {
          console.log('❌ Error parsing Firebase config');
          resolve({ secure: false, message: 'Config parsing error' });
        }
      });
    });

    req.on('error', (e) => {
      console.log('❌ Error accessing Firebase config:', e.message);
      resolve({ secure: false, message: 'Config access error' });
    });

    req.end();
  });
}

// Test clickjacking protection
async function testClickjackingProtection() {
  console.log('\n🖱️  Testing Clickjacking Protection...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: DOMAIN,
      port: 443,
      path: '/',
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      const xFrameOptions = res.headers['x-frame-options'];
      const csp = res.headers['content-security-policy'];
      
      let protected = false;
      let methods = [];
      
      if (xFrameOptions && (xFrameOptions.toUpperCase() === 'DENY' || xFrameOptions.toUpperCase() === 'SAMEORIGIN')) {
        protected = true;
        methods.push(`X-Frame-Options: ${xFrameOptions}`);
      }
      
      if (csp && csp.includes('frame-ancestors')) {
        protected = true;
        methods.push('CSP frame-ancestors directive');
      }
      
      if (protected) {
        console.log('✅ Clickjacking protection enabled');
        methods.forEach(method => console.log(`   ${method}`));
        resolve({ secure: true, message: 'Clickjacking protection active' });
      } else {
        console.log('❌ No clickjacking protection detected');
        resolve({ secure: false, message: 'No clickjacking protection' });
      }
    });

    req.on('error', (e) => {
      console.log('❌ Error testing clickjacking protection:', e.message);
      resolve({ secure: false, message: 'Test error' });
    });

    req.end();
  });
}

// Main test runner
async function runSecurityTests() {
  console.log('🚀 Starting Security Tests for', DOMAIN);
  console.log('=' .repeat(50));
  
  const results = {};
  
  try {
    results.headers = await testSecurityHeaders();
    results.cors = await testCORS();
    results.firebase = await testFirebaseConfig();
    results.clickjacking = await testClickjackingProtection();
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 SECURITY TEST SUMMARY');
    console.log('='.repeat(50));
    
    let totalTests = 0;
    let passedTests = 0;
    
    Object.keys(results).forEach(test => {
      totalTests++;
      if (results[test].secure !== false) {
        passedTests++;
        console.log(`✅ ${test.toUpperCase()}: ${results[test].message || 'PASSED'}`);
      } else {
        console.log(`❌ ${test.toUpperCase()}: ${results[test].message || 'FAILED'}`);
      }
    });
    
    console.log('\n📈 Overall Security Score:', `${passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All security tests passed!');
    } else {
      console.log('⚠️  Some security issues detected. Please review and fix.');
    }
    
  } catch (error) {
    console.error('❌ Error running security tests:', error);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runSecurityTests();
}

module.exports = { runSecurityTests };
