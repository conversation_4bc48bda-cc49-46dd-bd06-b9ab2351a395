import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { updateProfile } from "firebase/auth";
import { useToast } from "@/hooks/use-toast";
import {
  Company,
  Partner,
  FirmSettings,
  getCompanies,
  getPartners,
  getFirmSettings,
  addCompany,
  addPartner,
  saveFirmSettings,
  updateFirmSettings,
  updateCompany,
  updatePartner,
  deleteCompany,
  deletePartner,
  deleteFirmSettings
} from "@/lib/firebaseStorageService";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { Plus, Pencil, Trash2, Loader2, Save, Building, Settings as SettingsIcon } from "lucide-react";
import { format } from "date-fns";

const Settings = () => {
  const { currentUser } = useAuth();
  const { toast } = useToast();

  // Profile states
  const [name, setName] = useState(currentUser?.displayName || "");
  const [loading, setLoading] = useState(false);

  // Entity management states
  const [companies, setCompanies] = useState<Company[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [firmSettings, setFirmSettings] = useState<FirmSettings | null>(null);
  const [loadingEntities, setLoadingEntities] = useState({
    companies: false,
    partners: false,
    firmSettings: false
  });

  // Dialog states
  const [companyDialogOpen, setCompanyDialogOpen] = useState(false);
  const [partnerDialogOpen, setPartnerDialogOpen] = useState(false);

  // Form states
  const [newCompanyName, setNewCompanyName] = useState("");
  const [newPartnerName, setNewPartnerName] = useState("");
  const [newPartnerRegNumber, setNewPartnerRegNumber] = useState("");

  // Firm settings form states
  const [firmName, setFirmName] = useState("");
  const [firmRegNumber, setFirmRegNumber] = useState("");
  const [firmAddress, setFirmAddress] = useState("");
  const [firmContactEmail, setFirmContactEmail] = useState("");
  const [firmPhone, setFirmPhone] = useState("");
  const [firmPreferences, setFirmPreferences] = useState({
    emailNotifications: true,
    smsNotifications: false,
    autoBackup: true,
    dataRetentionPeriod: 12,
    defaultReportFormat: "PDF",
    theme: "light"
  });

  // Edit states
  const [editingPartner, setEditingPartner] = useState<Partner | null>(null);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);
  const [savingFirmSettings, setSavingFirmSettings] = useState(false);

  // Profile management
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser) {
      return;
    }

    setLoading(true);
    try {
      await updateProfile(currentUser, {
        displayName: name
      });

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated."
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };



  // Load entities on component mount
  useEffect(() => {
    if (currentUser) {
      fetchCompanies();
      fetchPartners();
      fetchFirmSettings();
    }
  }, [currentUser]);

  // Fetch firm settings
  const fetchFirmSettings = async () => {
    if (!currentUser) return;

    setLoadingEntities(prev => ({ ...prev, firmSettings: true }));
    try {
      const settings = await getFirmSettings(currentUser.uid);
      if (settings) {
        setFirmSettings(settings);
        setFirmName(settings.firmName);
        setFirmRegNumber(settings.registrationNumber);
        setFirmAddress(settings.address || "");
        setFirmContactEmail(settings.contactEmail || "");
        setFirmPhone(settings.phone || "");
        if (settings.preferences) {
          setFirmPreferences(settings.preferences);
        }
      }
    } catch (error) {
      console.error("Error fetching firm settings:", error);
      toast({
        title: "Error",
        description: "Failed to load firm settings",
        variant: "destructive"
      });
    } finally {
      setLoadingEntities(prev => ({ ...prev, firmSettings: false }));
    }
  };

  // Fetch partners
  const fetchPartners = async () => {
    if (!currentUser) return;

    setLoadingEntities(prev => ({ ...prev, partners: true }));
    try {
      const fetchedPartners = await getPartners(currentUser.uid);
      setPartners(fetchedPartners);
    } catch (error) {
      console.error("Error fetching partners:", error);
      toast({
        title: "Error",
        description: "Failed to load partners",
        variant: "destructive"
      });
    } finally {
      setLoadingEntities(prev => ({ ...prev, partners: false }));
    }
  };

  // Fetch companies
  const fetchCompanies = async () => {
    if (!currentUser) return;

    setLoadingEntities(prev => ({ ...prev, companies: true }));
    try {
      const fetchedCompanies = await getCompanies(currentUser.uid);
      setCompanies(fetchedCompanies);
    } catch (error) {
      console.error("Error fetching companies:", error);
      toast({
        title: "Error",
        description: "Failed to load companies",
        variant: "destructive"
      });
    } finally {
      setLoadingEntities(prev => ({ ...prev, companies: false }));
    }
  };

  // Handle firm settings
  const handleSaveFirmSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;

    setSavingFirmSettings(true);
    try {
      if (firmSettings) {
        // Update existing settings
        await updateFirmSettings(currentUser.uid, {
          firmName,
          registrationNumber: firmRegNumber,
          address: firmAddress,
          contactEmail: firmContactEmail,
          phone: firmPhone,
          preferences: firmPreferences
        });
      } else {
        // Create new settings
        await saveFirmSettings(
          currentUser.uid,
          firmName,
          firmRegNumber,
          firmAddress,
          firmContactEmail,
          firmPhone,
          firmPreferences
        );
      }

      await fetchFirmSettings();
      toast({
        title: "Success",
        description: "Firm settings saved successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to save firm settings",
        variant: "destructive"
      });
    } finally {
      setSavingFirmSettings(false);
    }
  };

  // Handle company operations
  const handleAddCompany = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !newCompanyName.trim()) return;

    try {
      await addCompany(currentUser.uid, newCompanyName);
      setNewCompanyName("");
      setCompanyDialogOpen(false);
      await fetchCompanies();
      toast({
        title: "Success",
        description: "Company added successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add company",
        variant: "destructive"
      });
    }
  };

  const handleUpdateCompany = async (companyId: string, newName: string) => {
    if (!currentUser) return;

    try {
      await updateCompany(currentUser.uid, companyId, newName);
      await fetchCompanies();
      setEditingCompany(null);
      toast({
        title: "Success",
        description: "Company updated successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update company",
        variant: "destructive"
      });
    }
  };

  const handleDeleteCompany = async (companyId: string) => {
    if (!currentUser) return;

    try {
      await deleteCompany(currentUser.uid, companyId);
      await fetchCompanies();
      toast({
        title: "Success",
        description: "Company deleted successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete company",
        variant: "destructive"
      });
    }
  };

  // Handle partner operations
  const handleAddPartner = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !newPartnerName.trim() || !newPartnerRegNumber.trim()) return;

    try {
      await addPartner(currentUser.uid, newPartnerName, newPartnerRegNumber);
      setNewPartnerName("");
      setNewPartnerRegNumber("");
      setPartnerDialogOpen(false);
      await fetchPartners();
      toast({
        title: "Success",
        description: "Partner added successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add partner",
        variant: "destructive"
      });
    }
  };

  const handleUpdatePartner = async (partnerId: string, newName: string, newRegNumber: string) => {
    if (!currentUser) return;

    try {
      await updatePartner(currentUser.uid, partnerId, newName, newRegNumber);
      await fetchPartners();
      setEditingPartner(null);
      toast({
        title: "Success",
        description: "Partner updated successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update partner",
        variant: "destructive"
      });
    }
  };

  const handleDeletePartner = async (partnerId: string) => {
    if (!currentUser) return;

    try {
      await deletePartner(currentUser.uid, partnerId);
      await fetchPartners();
      toast({
        title: "Success",
        description: "Partner deleted successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete partner",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex items-center space-x-2">
        <SettingsIcon className="h-6 w-6" />
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Tabs defaultValue="account" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="companies">Companies</TabsTrigger>
          <TabsTrigger value="partners">Partners</TabsTrigger>
          <TabsTrigger value="firm">Firm Settings</TabsTrigger>
        </TabsList>

        {/* Account Tab */}
        <TabsContent value="account" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Update your personal information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <form onSubmit={handleUpdateProfile} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Your full name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    value={currentUser?.email || ""}
                    readOnly
                    className="bg-gray-50"
                  />
                  <p className="text-sm text-gray-500">Your email cannot be changed.</p>
                </div>
                <Button type="submit" disabled={loading}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Profile
                </Button>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Authentication</CardTitle>
              <CardDescription>Your account uses Microsoft authentication</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Your account is authenticated through Microsoft. Password management is handled by your Microsoft account.
              </p>
              <p className="text-sm text-gray-500">
                To change your password, please visit your Microsoft account settings.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Companies Tab */}
        <TabsContent value="companies" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Companies</CardTitle>
                <CardDescription>Manage your company list</CardDescription>
              </div>
              <Dialog open={companyDialogOpen} onOpenChange={setCompanyDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Company
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Company</DialogTitle>
                    <DialogDescription>Enter company details below</DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleAddCompany} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        value={newCompanyName}
                        onChange={(e) => setNewCompanyName(e.target.value)}
                        placeholder="Company name"
                        required
                      />
                    </div>
                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setCompanyDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit">Add Company</Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {loadingEntities.companies ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading companies...</span>
                </div>
              ) : companies.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Building className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No companies found. Add your first company.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Company Name</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {companies.map((company) => (
                      <TableRow key={company.id}>
                        <TableCell>
                          {editingCompany?.id === company.id ? (
                            <Input
                              value={editingCompany.name}
                              onChange={(e) => setEditingCompany({
                                ...editingCompany,
                                name: e.target.value
                              })}
                              onBlur={() => handleUpdateCompany(company.id, editingCompany.name)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleUpdateCompany(company.id, editingCompany.name);
                                }
                                if (e.key === 'Escape') {
                                  setEditingCompany(null);
                                }
                              }}
                              autoFocus
                            />
                          ) : (
                            <span>{company.name}</span>
                          )}
                        </TableCell>
                        <TableCell>{format(new Date(company.createdAt), "MMM d, yyyy")}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingCompany(company)}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button size="sm" variant="destructive">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Company</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{company.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeleteCompany(company.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Partners Tab */}
        <TabsContent value="partners" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Partners</CardTitle>
                <CardDescription>Manage your partner list</CardDescription>
              </div>
              <Dialog open={partnerDialogOpen} onOpenChange={setPartnerDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Partner
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Partner</DialogTitle>
                    <DialogDescription>Enter partner details below</DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleAddPartner} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="partnerName">Partner Name</Label>
                      <Input
                        id="partnerName"
                        value={newPartnerName}
                        onChange={(e) => setNewPartnerName(e.target.value)}
                        placeholder="Partner name"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="partnerRegNumber">Registration Number</Label>
                      <Input
                        id="partnerRegNumber"
                        value={newPartnerRegNumber}
                        onChange={(e) => setNewPartnerRegNumber(e.target.value)}
                        placeholder="Registration number"
                        required
                      />
                    </div>
                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setPartnerDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit">Add Partner</Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {loadingEntities.partners ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading partners...</span>
                </div>
              ) : partners.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Building className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No partners found. Add your first partner.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Partner Name</TableHead>
                      <TableHead>Registration Number</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {partners.map((partner) => (
                      <TableRow key={partner.id}>
                        <TableCell>
                          {editingPartner?.id === partner.id ? (
                            <Input
                              value={editingPartner.name}
                              onChange={(e) => setEditingPartner({
                                ...editingPartner,
                                name: e.target.value
                              })}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleUpdatePartner(partner.id, editingPartner.name, editingPartner.registrationNumber);
                                }
                                if (e.key === 'Escape') {
                                  setEditingPartner(null);
                                }
                              }}
                              autoFocus
                            />
                          ) : (
                            <span>{partner.name}</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {editingPartner?.id === partner.id ? (
                            <Input
                              value={editingPartner.registrationNumber}
                              onChange={(e) => setEditingPartner({
                                ...editingPartner,
                                registrationNumber: e.target.value
                              })}
                              onBlur={() => handleUpdatePartner(partner.id, editingPartner.name, editingPartner.registrationNumber)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleUpdatePartner(partner.id, editingPartner.name, editingPartner.registrationNumber);
                                }
                                if (e.key === 'Escape') {
                                  setEditingPartner(null);
                                }
                              }}
                            />
                          ) : (
                            <span>{partner.registrationNumber}</span>
                          )}
                        </TableCell>
                        <TableCell>{format(new Date(partner.createdAt), "MMM d, yyyy")}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingPartner(partner)}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button size="sm" variant="destructive">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Partner</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{partner.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeletePartner(partner.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Firm Settings Tab */}
        <TabsContent value="firm" className="space-y-6">
          {loadingEntities.firmSettings ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading firm settings...</span>
              </CardContent>
            </Card>
          ) : (
            <form onSubmit={handleSaveFirmSettings} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Firm Information</CardTitle>
                  <CardDescription>Update your firm's basic information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firmName">Firm Name *</Label>
                      <Input
                        id="firmName"
                        value={firmName}
                        onChange={(e) => setFirmName(e.target.value)}
                        placeholder="Your firm name"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="firmRegNumber">Registration Number *</Label>
                      <Input
                        id="firmRegNumber"
                        value={firmRegNumber}
                        onChange={(e) => setFirmRegNumber(e.target.value)}
                        placeholder="Registration number"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="firmAddress">Address</Label>
                    <Input
                      id="firmAddress"
                      value={firmAddress}
                      onChange={(e) => setFirmAddress(e.target.value)}
                      placeholder="Business address"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firmContactEmail">Contact Email</Label>
                      <Input
                        id="firmContactEmail"
                        type="email"
                        value={firmContactEmail}
                        onChange={(e) => setFirmContactEmail(e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="firmPhone">Phone</Label>
                      <Input
                        id="firmPhone"
                        value={firmPhone}
                        onChange={(e) => setFirmPhone(e.target.value)}
                        placeholder="+****************"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Preferences</CardTitle>
                  <CardDescription>Configure your application preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Email Notifications</Label>
                        <p className="text-sm text-gray-500">Receive email notifications for important updates</p>
                      </div>
                      <Switch
                        checked={firmPreferences.emailNotifications}
                        onCheckedChange={(checked) => setFirmPreferences(prev => ({
                          ...prev,
                          emailNotifications: checked
                        }))}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>SMS Notifications</Label>
                        <p className="text-sm text-gray-500">Receive SMS notifications for urgent alerts</p>
                      </div>
                      <Switch
                        checked={firmPreferences.smsNotifications}
                        onCheckedChange={(checked) => setFirmPreferences(prev => ({
                          ...prev,
                          smsNotifications: checked
                        }))}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Auto Backup</Label>
                        <p className="text-sm text-gray-500">Automatically backup your data</p>
                      </div>
                      <Switch
                        checked={firmPreferences.autoBackup}
                        onCheckedChange={(checked) => setFirmPreferences(prev => ({
                          ...prev,
                          autoBackup: checked
                        }))}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="dataRetention">Data Retention (months)</Label>
                      <Select
                        value={firmPreferences.dataRetentionPeriod.toString()}
                        onValueChange={(value) => setFirmPreferences(prev => ({
                          ...prev,
                          dataRetentionPeriod: parseInt(value)
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="6">6 months</SelectItem>
                          <SelectItem value="12">12 months</SelectItem>
                          <SelectItem value="24">24 months</SelectItem>
                          <SelectItem value="36">36 months</SelectItem>
                          <SelectItem value="60">60 months</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="reportFormat">Default Report Format</Label>
                      <Select
                        value={firmPreferences.defaultReportFormat}
                        onValueChange={(value) => setFirmPreferences(prev => ({
                          ...prev,
                          defaultReportFormat: value
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PDF">PDF</SelectItem>
                          <SelectItem value="Excel">Excel</SelectItem>
                          <SelectItem value="Word">Word</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="theme">Theme</Label>
                    <Select
                      value={firmPreferences.theme}
                      onValueChange={(value) => setFirmPreferences(prev => ({
                        ...prev,
                        theme: value
                      }))}
                    >
                      <SelectTrigger className="w-full md:w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={savingFirmSettings}>
                    {savingFirmSettings && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Save className="mr-2 h-4 w-4" />
                    Save Firm Settings
                  </Button>
                </CardFooter>
              </Card>
            </form>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
