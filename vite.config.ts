import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 5173,
    // Configure headers for development server
    headers: {
      // Remove restrictive CSP in development to allow Firebase
      'Content-Security-Policy': '',
      // Allow cross-origin requests for Firebase
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      onwarn(warning, warn) {
        // Ignore source map warnings from node_modules
        if (
          warning.code === 'SOURCEMAP_ERROR' &&
          warning.message.includes('node_modules')
        ) {
          return;
        }
        warn(warning);
      },
    },
  },
  optimizeDeps: {
    // Include Firebase modules for optimization
    include: [
      'firebase/app',
      'firebase/auth',
      'firebase/database',
      'firebase/storage',
    ],
    esbuildOptions: {
      logOverride: {
        'empty-source-map': 'silent',
      },
      // Define global variables for Firebase
      define: {
        global: 'globalThis',
      },
    },
  },
  // Define environment variables
  define: {
    global: 'globalThis',
    // Ensure process.env is available for Firebase
    'process.env': {},
  },
}));
