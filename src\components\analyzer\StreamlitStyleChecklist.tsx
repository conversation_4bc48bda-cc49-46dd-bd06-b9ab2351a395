// StreamlitStyleChecklist.tsx - Updated to work with new check definitions

import React from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";

// Import new types
import { CheckResult } from "@/lib/checkDefinitions";
import { AnalysisParameters, DocumentFiles } from "@/lib/mainDocumentProcessor";
import CheckItem from "./CheckItem";

// Helper function to format JSON data as readable text
const formatJsonAsText = (data: any): string => {
  if (!data) return '';

  try {
    let parsedData = data;

    // If it's a string, try to parse it as JSON
    if (typeof data === 'string') {
      // Check if it looks like JSO<PERSON> (starts with { or [)
      const trimmed = data.trim();
      if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
        try {
          parsedData = JSON.parse(data);
        } catch (parseError) {
          // If JSON parsing fails, return the original string
          return data;
        }
      } else {
        // If it doesn't look like JSO<PERSON>, return as is
        return data;
      }
    }

    // If it's an object, format it nicely
    if (typeof parsedData === 'object' && parsedData !== null) {
      let formatted = '';

      for (const [key, value] of Object.entries(parsedData)) {
        // Convert camelCase and snake_case to readable format
        const readableKey = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/_/g, ' ')
          .replace(/^./, str => str.toUpperCase())
          .trim();

        if (typeof value === 'object' && value !== null) {
          formatted += `${readableKey}:\n`;
          for (const [subKey, subValue] of Object.entries(value)) {
            const readableSubKey = subKey
              .replace(/([A-Z])/g, ' $1')
              .replace(/_/g, ' ')
              .replace(/^./, str => str.toUpperCase())
              .trim();

            if (typeof subValue === 'boolean') {
              formatted += `  ${readableSubKey}: ${subValue ? 'Yes' : 'No'}\n`;
            } else if (subValue === null || subValue === undefined) {
              formatted += `  ${readableSubKey}: N/A\n`;
            } else {
              formatted += `  ${readableSubKey}: ${subValue}\n`;
            }
          }
          formatted += '\n';
        } else {
          if (typeof value === 'boolean') {
            formatted += `${readableKey}: ${value ? 'Yes' : 'No'}\n`;
          } else if (value === null || value === undefined) {
            formatted += `${readableKey}: N/A\n`;
          } else {
            // Handle special formatting for currency values
            if (typeof value === 'string' && value.includes('₹')) {
              formatted += `${readableKey}: ${value}\n`;
            } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
              formatted += `${readableKey}: ₹${value} crores\n`;
            } else {
              // Handle long text values by truncating if needed
              const stringValue = String(value);
              if (stringValue.length > 200) {
                formatted += `${readableKey}: ${stringValue.substring(0, 200)}...\n`;
              } else {
                formatted += `${readableKey}: ${stringValue}\n`;
              }
            }
          }
        }
      }

      return formatted.trim();
    }

    return String(parsedData);
  } catch (error) {
    console.warn('Error formatting JSON data:', error);
    return String(data);
  }
};

interface StreamlitStyleChecklistProps {
  results: Record<string, CheckResult>;
  parameters: AnalysisParameters;
  documents?: DocumentFiles;
}

// Map check IDs to user-friendly display names
const CHECK_DISPLAY_NAMES: Record<string, string> = {
  // Independent Checks
  audit_title: "Audit Title - Independent Auditor's Report",
  address_to_members: "Address to Members Format",
  audit_report_date: "Audit Report Date Verification",
  standalone_consolidated_wording: "Standalone/Consolidated Wording Consistency",
  brsr_brr_check: "BRSR/BRR Check",
  key_audit_matters: "Key Audit Matters Presence",
  audit_trail_software: "Audit-Trail Accounting Software",
  section_197_16: "Section 197(16) Reference",
  company_name_consistency: "Company Name Consistency",
  pkf_signature_block: "PKF Signature Block Verification",
  clause_20: "Clause 20 - Annexure A(CARO)",
  clause_21: "Clause 21 - Annexure A(CARO)",
  benami_property_clause: "Benami Property Clause",


  // Conditional Checks
  // caro_clause_xxi: "CARO Clause (xxi) - Consolidated Reports",
  consolidated_wording_consistency: "Consolidated Wording Consistency",
  nbfc_caro_exemptions: "NBFC CARO Exemptions",

  // Interlinked Checks
  ar_caro_reference_matching: "Audit Report + CARO Reference Matching",
  ar_ifc_reference_matching: "Audit Report + IFC Reference Matching",
  bs_caro_ppe_check: "Balance Sheet + CARO PPE Check",
  bs_caro_intangible_check: "Balance Sheet + CARO Intangible Assets Check",
  bs_caro_inventory_check: "Balance Sheet + CARO Inventory Check",
  bs_caro_secured_borrowings: "Balance Sheet + CARO Secured Borrowings Check",
  bs_caro_fixed_deposits: "Balance Sheet + CARO Fixed Deposits Check",
  notes_caro_immovable_property: "Notes + CARO Immovable Property Check",
  notes_caro_new_investments: "Notes + CARO New Investments/Loans Check",
  notes_caro_contingent_liabilities: "Notes + CARO Contingent Liabilities Check",
  notes_caro_goods_in_transit: "Notes + CARO Goods in Transit Check",
  notes_caro_aggregate_threshold: "Notes + CARO Aggregate Threshold Check",
  // notes_caro_rpt: "Notes + CARO Related Party Transactions Check", // Covered by notes_caro_related_party_loans_alignment
  notes_caro_related_party_loans_alignment: "Notes + CARO Related Party Loans Alignment",
  notes_caro_statutory_dues_static: "Notes + CARO Static Statutory Dues Analysis",
  notes_caro_revaluation_reserve_check: "Notes + CARO Revaluation Reserve Check",
  audit_report_caro_material_uncertainty: "Audit Report Material Uncertainty + CARO (xix) Alignment",
  audit_muga_s143_ifc_inadequate: "Audit Report MUGC S143 Ref + IFC Annexure B 'Inadequate' Opinion"
};

// Group checks by category
const getChecksByCategory = (results: Record<string, CheckResult>) => {
  const independent: Array<[string, CheckResult]> = [];
  const conditional: Array<[string, CheckResult]> = [];
  const interlinked: Array<[string, CheckResult]> = [];

  Object.entries(results).forEach(([checkId, result]) => {
    // Categorize based on check ID patterns and source
    if (checkId.includes('_caro_') ||
        checkId.includes('_ifc_') ||
        (checkId.includes('ar_') && checkId.includes('reference')) ||
        checkId.includes('inventory_goods_in_transit_check') ||
        checkId.includes('notes_caro_') ||
        checkId.includes('enhanced_') ||
        result.source === 'multi-document' ||
        result.source === 'interlinked') {
      interlinked.push([checkId, result]);
    } else if (checkId.includes('consolidated') || checkId.includes('nbfc') || checkId.includes('clause_xxi')) {
      conditional.push([checkId, result]);
    } else {
      independent.push([checkId, result]);
    }
  });

  return { independent, conditional, interlinked };
};

const StreamlitStyleChecklist: React.FC<StreamlitStyleChecklistProps> = ({
  results,
  parameters,
  documents = {}
}) => {
  // Check for missing or invalid data
  if (!results || Object.keys(results).length === 0) {
    return (
      <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
        <div className="bg-red-500 text-white p-4 rounded-md">
          <h2 className="text-xl font-bold mb-2">Analysis Failed</h2>
          <p>No analysis results available. Please try again or contact support if the issue persists.</p>
        </div>
      </div>
    );
  }

  // Check for date-related issues
  try {
    if (parameters.audit_date) {
      if (parameters.audit_date instanceof Date) {
        parameters.audit_date.getTime();
      } else if (typeof parameters.audit_date === 'number') {
        new Date(parameters.audit_date).getTime();
      } else if (typeof parameters.audit_date === 'string') {
        new Date(parameters.audit_date).getTime();
      } else {
        throw new Error("Invalid date format");
      }
    }
  } catch (err) {
    console.error("Date validation error in StreamlitStyleChecklist:", err);
    return (
      <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
        <div className="bg-red-500 text-white p-4 rounded-md">
          <h2 className="text-xl font-bold mb-2">Analysis Failed</h2>
          <p>An error occurred during analysis: Cannot read properties of undefined (reading 'getTime')</p>
          <p className="mt-2 text-sm">This is likely due to an invalid date format. Please try again with a valid date.</p>
        </div>
      </div>
    );
  }

  // Count the number of compliant checks
  const compliantCount = Object.values(results).filter(result => result.isCompliant).length;
  const totalChecks = Object.keys(results).length;
  const compliancePercentage = totalChecks > 0 ? Math.round((compliantCount / totalChecks) * 100) : 0;

  // Group checks by category
  const { independent, conditional, interlinked } = getChecksByCategory(results);

  // Render check item with compact summary and expandable details
  const renderCheckItem = (checkId: string, result: CheckResult) => {
    const displayName = CHECK_DISPLAY_NAMES[checkId] || checkId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <Accordion type="single" collapsible key={checkId} className="mb-2">
        <AccordionItem value={checkId} className="border border-gray-200 rounded-md overflow-hidden">
          <AccordionTrigger className="py-3 px-4 hover:bg-gray-50">
            <div className="flex items-center justify-between w-full mr-2">
              <div className="flex items-center">
                {result.isCompliant ? (
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
                )}
                <span className="font-medium text-left">{displayName}</span>
              </div>
              <div className="flex items-center">
                {result.isCompliant ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    ✓ Compliant
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    ✗ Non-Compliant
                  </span>
                )}
              </div>
            </div>
          </AccordionTrigger>
          
          <AccordionContent>
            <div className="px-4 pb-4 pt-2 bg-gray-50">
              {/* Status Summary */}
              <div className="mb-4 p-3 bg-white rounded-md border-l-4 border-l-blue-500">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-semibold text-gray-900">Status Summary</h5>
                  {result.isCompliant ? (
                    <span className="text-green-600 font-medium">Full Compliance</span>
                  ) : (
                    <span className="text-red-600 font-medium">Requires Attention</span>
                  )}
                </div>
                {result.isCompliant ? (
                  <p className="text-sm text-green-700">
                    This check has passed all compliance requirements.
                  </p>
                ) : (
                  <p className="text-sm text-red-700">
                    This check did not meet compliance requirements and needs review.
                  </p>
                )}
              </div>

              {/* Detailed Explanation */}
              <div className="mb-4">
                <h5 className="font-semibold text-gray-900 mb-2">Explanation</h5>
                <div className="bg-white p-3 rounded-md border">
                  <div className="text-sm text-gray-700 whitespace-pre-line">
                    {formatJsonAsText(result.explanation)}
                  </div>
                </div>
              </div>

              {/* Extracted Data Section */}
              {result.extractedData && (
                <div className="mb-4">
                  <h5 className="font-semibold text-gray-900 mb-2">Exact Text Found</h5>
                  <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                    <div className="text-xs font-mono text-blue-900 whitespace-pre-wrap">
                      {formatJsonAsText(result.extractedData)}
                    </div>
                  </div>
                </div>
              )}

              {/* Additional Details */}
              {result.detail && (
                <div className="mb-4">
                  <h5 className="font-semibold text-gray-900 mb-2">Compliance Details</h5>
                  <div className="bg-white p-3 rounded-md border">
                    <div className="text-xs text-gray-600 whitespace-pre-wrap">
                      {formatJsonAsText(result.detail)}
                    </div>
                  </div>
                </div>
              )}

              {/* Recommendation Section */}
              {!result.isCompliant && (
                <div className="mt-4 p-3 bg-yellow-50 rounded-md border border-yellow-200">
                  <h5 className="font-semibold text-yellow-800 mb-1">Recommendation</h5>
                  <p className="text-sm text-yellow-700">
                    Review the explanation above and ensure the required information is properly included in your document.
                  </p>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    );
  };

  return (
    <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
      {/* Header with compliance summary */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">Analysis Results</h2>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {compliantCount}/{totalChecks}
            </div>
            <div className="text-sm text-gray-600">Checks Passed</div>
          </div>
        </div>

        {/* Compliance progress bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Compliance Rate</span>
            <span className={`text-lg font-bold ${
              compliancePercentage < 50 ? "text-red-600" :
              compliancePercentage < 80 ? "text-yellow-600" : "text-green-600"
            }`}>
              {compliancePercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-500 ${
                compliancePercentage < 50 ? "bg-red-500" :
                compliancePercentage < 80 ? "bg-yellow-500" : "bg-green-500"
              }`}
              style={{ width: `${compliancePercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Quick stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="bg-white p-3 rounded-md border">
            <div className="text-lg font-bold text-green-600">{independent.filter(([, result]) => result.isCompliant).length}</div>
            <div className="text-xs text-gray-600">Independent Passed</div>
          </div>
          <div className="bg-white p-3 rounded-md border">
            <div className="text-lg font-bold text-purple-600">{interlinked.filter(([, result]) => result.isCompliant).length}</div>
            <div className="text-xs text-gray-600">Interlinked Passed</div>
          </div>
          <div className="bg-white p-3 rounded-md border">
            <div className="text-lg font-bold text-orange-600">{conditional.filter(([, result]) => result.isCompliant).length}</div>
            <div className="text-xs text-gray-600">Conditional Passed</div>
          </div>
        </div>
      </div>

      {/* Results organized by category */}
      <Accordion type="multiple" defaultValue={["independent"]}>

        {/* Independent Document Checks */}
        {independent.length > 0 && (
          <AccordionItem value="independent" className="border border-gray-200 rounded-md mb-4 overflow-hidden">
            <AccordionTrigger className="text-lg font-bold py-4 px-6 bg-blue-50 hover:bg-blue-100">
              <div className="flex items-center justify-between w-full mr-2">
                <span>1) Independent Document Checks ({independent.length})</span>
                <span className="text-sm font-normal text-gray-600">
                  {independent.filter(([, result]) => result.isCompliant).length}/{independent.length} passed
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="p-4">
                <div className="mb-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
                  <strong>Single Document Analysis:</strong> These checks analyze individual documents for compliance with regulations and standards.
                </div>
                <div className="space-y-2">
                  {independent.map(([checkId, result]) => renderCheckItem(checkId, result))}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Interlinked Document Checks */}
        {interlinked.length > 0 && (
          <AccordionItem value="interlinked" className="border border-gray-200 rounded-md mb-4 overflow-hidden">
            <AccordionTrigger className="text-lg font-bold py-4 px-6 bg-purple-50 hover:bg-purple-100">
              <div className="flex items-center justify-between w-full mr-2">
                <span>2) Interlinked Document Checks ({interlinked.length})</span>
                <span className="text-sm font-normal text-gray-600">
                  {interlinked.filter(([, result]) => result.isCompliant).length}/{interlinked.length} passed
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="p-4">
                <div className="mb-4 text-sm text-gray-600 bg-purple-50 p-3 rounded-md">
                  <strong>Multi-Document Comparisons:</strong> These checks verify consistency and accuracy across multiple related documents.
                </div>
                <div className="space-y-2">
                  {interlinked.map(([checkId, result]) => renderCheckItem(checkId, result))}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Conditional Checks */}
        {conditional.length > 0 && (
          <AccordionItem value="conditional" className="border border-gray-200 rounded-md mb-4 overflow-hidden">
            <AccordionTrigger className="text-lg font-bold py-4 px-6 bg-orange-50 hover:bg-orange-100">
              <div className="flex items-center justify-between w-full mr-2">
                <span>3) Conditional Checks ({conditional.length})</span>
                <span className="text-sm font-normal text-gray-600">
                  {conditional.filter(([, result]) => result.isCompliant).length}/{conditional.length} passed
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="p-4">
                <div className="mb-4 text-sm text-gray-600 bg-orange-50 p-3 rounded-md">
                  <strong>Based on User Selections:</strong> These checks are applied based on your specific company parameters and selections.
                  <div className="mt-2">
                    <strong>Applied Conditions:</strong>
                    <ul className="mt-1 list-disc list-inside text-xs">
                      <li>Report Type: {parameters.audit_report_type}</li>
                      <li>Company Status: {parameters.company_listing_status}</li>
                      {parameters.company_listing_status === 'Listed' && (
                        <li>Top 1000/500: {parameters.top_1000_or_500}</li>
                      )}
                      <li>NBFC Status: {parameters.is_nbfc}</li>
                      <li>Has CFS: {parameters.has_cfs}</li>
                    </ul>
                  </div>
                </div>
                <div className="space-y-2">
                  {conditional.map(([checkId, result]) => renderCheckItem(checkId, result))}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>

      {/* Summary information */}
      <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
        <div className="flex items-center">
          <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
          <div>
            <h3 className="font-semibold text-green-800">Analysis Complete</h3>
            <p className="text-sm text-green-700">
              {compliantCount} out of {totalChecks} checks passed ({compliancePercentage}% compliance rate).
              {compliancePercentage === 100 
                ? " Excellent! All checks are compliant." 
                : compliancePercentage >= 80 
                ? " Good compliance rate, review non-compliant items." 
                : " Several items need attention for better compliance."
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StreamlitStyleChecklist;