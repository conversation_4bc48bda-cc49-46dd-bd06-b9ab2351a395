import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import LoadingScreen from "@/components/common/LoadingScreen";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [hasRedirected, setHasRedirected] = useState(false);
  
  // Handle authentication state changes
  useEffect(() => {
    // Only redirect if we have a confirmed logged-in user and haven't redirected yet
    if (currentUser && !loading && !hasRedirected) {
      setHasRedirected(true);
      navigate('/dashboard', { replace: true });
    }
  }, [currentUser, loading, navigate, hasRedirected]);

  // Reset redirect flag when location changes
  useEffect(() => {
    setHasRedirected(false);
  }, [location.pathname]);

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen />;
  }

  // If user is logged in, show loading while redirecting
  if (currentUser && !hasRedirected) {
    return <LoadingScreen />;
  }

  // Show auth pages only if user is not logged in
  if (!currentUser) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }

  // Fallback loading screen
  return <LoadingScreen />;
};

export default AuthLayout;